import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Brain, FileSpreadsheet, Eye, Download, Play, ChevronDown, X, Calendar, Clock, AlertCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useDashboard } from '../context/DashboardContext';
import scanApi from '../services/apiService';

interface ScanFormData {
  organization: string;
  projectName: string;
  targetAsset: string;
  scanTarget: string;
  label?: string;
  schedule: 'now' | 'later';
  scheduledDate?: string;
  scheduledTime?: string;
  disclaimer: boolean;
  email?: string;
  password?: string;
  loginUrl?: string; // <-- Add this line
}

interface Scan {
  id: number;
  asset: string;
  organization: string;
  project: string;
  type: string;
  startDate: string;
  startTime: string;
  endDate: string;
  endTime: string;
  status: string;
  description: string;
  details: {
    vulnerabilities: number;
    endpoints: number;
    openPorts: number;
    severity: {
      high: number;
      medium: number;
      low: number;
    };
  };
}

const AssessmentCenter: React.FC = () => {
  const navigate = useNavigate();
  const { startScanPolling, activeScanIds } = useDashboard();
  const [activeDropdown, setActiveDropdown] = useState<number | null>(null);
  const [showScanForm, setShowScanForm] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<number>>(new Set());
  const [loadingData, setLoadingData] = useState(false);
  const [authScan, setAuthScan] = useState(false);
  const [scanFormData, setScanFormData] = useState<ScanFormData>({
    organization: '',
    projectName: '',
    targetAsset: 'web',
    scanTarget: '',
    label: '',
    schedule: 'now',
    disclaimer: false,
    email: '',
    password: '',
    loginUrl: '', // <-- Add this line
  });
  const [recentScans, setRecentScans] = useState<Scan[]>([]);

  // Load recent scans from localStorage
  useEffect(() => {
    const loadedScans = JSON.parse(localStorage.getItem('recentScans') || '[]');
    setRecentScans(loadedScans);
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeDropdown !== null) {
        const target = event.target as HTMLElement;
        if (!target.closest('.dropdown-container')) {
          setActiveDropdown(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [activeDropdown]);

  const handleActionClick = (scanId: number, action: 'run' | 'view' | 'download') => {
    console.log(`${action} scan ${scanId}`);
    setActiveDropdown(null);
  };

  // Function to handle scan initiation
  const initiateScan = async (scanData: ScanFormData) => {
    try {
      // Show loading state
      setLoadingData(true);
      
      // Call API to initiate scan
      const response = await scanApi.initiateScan(scanData.scanTarget);
      const scanId = response.scan_id;
      
      // Create a new scan entry
      const newScan: Scan = {
        id: parseInt(scanId) || Date.now(),
        asset: scanData.scanTarget,
        organization: scanData.organization,
        project: scanData.projectName,
        type: 'AI Scan',
        startDate: new Date().toISOString().split('T')[0],
        startTime: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
        endDate: '—',
        endTime: '—',
        status: 'pending',
        description: `Security assessment for ${scanData.targetAsset}: ${scanData.scanTarget}`,
        details: {
          vulnerabilities: 0,
          endpoints: 0,
          openPorts: 0,
          severity: { high: 0, medium: 0, low: 0 }
        }
      };

      // Add the new scan to the list
      setRecentScans(prevScans => {
        const updatedScans = [newScan, ...prevScans];
        localStorage.setItem('recentScans', JSON.stringify(updatedScans));
        return updatedScans;
      });
      
      // Start persistent polling through the Dashboard context
      startScanPolling(scanId.toString());
      
    } catch (error) {
      console.error('Error initiating scan:', error);
      // Show error message
    } finally {
      setLoadingData(false);
      setShowScanForm(false);
    }
  };

  // Effect to update UI when scan status changes
  useEffect(() => {
    const checkScansStatus = async () => {
      console.log("Checking status for active scans:", activeScanIds);
      
      if (activeScanIds.length === 0 || recentScans.length === 0) {
        return; // No scans to check
      }
      
      let hasUpdates = false;
      const updatedScans = [...recentScans]; // Create a new array to avoid mutation issues
      
      // For each active scan ID, fetch the latest data
      for (const scanIdStr of activeScanIds) {
        const scanId = parseInt(scanIdStr);
        const scanIndex = updatedScans.findIndex(scan => scan.id === scanId);
        
        if (scanIndex === -1) continue; // Skip if scan not found in recent scans
        
        // Skip already completed scans
        if (updatedScans[scanIndex].status === 'completed' || 
            updatedScans[scanIndex].status === 'failed') {
          continue;
        }
        
        try {
          console.log(`Fetching latest data for scan ${scanIdStr}...`);
          const scanData = await scanApi.getScanById(scanIdStr);
          
          if (scanData) {
            const previousStatus = updatedScans[scanIndex].status;
            const newStatus = scanData.status || 'in-progress';
            
            // Only update if something changed
            if (previousStatus !== newStatus || 
                updatedScans[scanIndex].details.vulnerabilities !== (scanData.alerts?.length || 0)) {
              
              console.log(`Scan ${scanIdStr} status changed from ${previousStatus} to ${newStatus}`);
              hasUpdates = true;
              
              // Create a new object to ensure React sees the change
              updatedScans[scanIndex] = {
                ...updatedScans[scanIndex],
                status: newStatus,
                endDate: newStatus === 'completed' ? new Date().toISOString().split('T')[0] : updatedScans[scanIndex].endDate,
                endTime: newStatus === 'completed' ? new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) : updatedScans[scanIndex].endTime,
                details: {
                  vulnerabilities: scanData.alerts?.length || 0,
                  endpoints: scanData.endpoints?.total_count || 0, 
                  openPorts: scanData.attack_surface_index?.metrics?.open_ports_count || 0,
                  severity: {
                    high: scanData.alerts?.filter((a: any) => a.riskcode === "3").length || 0,
                    medium: scanData.alerts?.filter((a: any) => a.riskcode === "2").length || 0,
                    low: scanData.alerts?.filter((a: any) => a.riskcode === "1").length || 0
                  }
                }
              };
            }
          }
        } catch (error) {
          console.error(`Error checking scan ${scanIdStr} status:`, error);
        }
      }
      
      // Update localStorage and state if any scan changed
      if (hasUpdates) {
        console.log("Updating state with new scan data");
        localStorage.setItem('recentScans', JSON.stringify(updatedScans));
        setRecentScans(updatedScans);
      }
    };
    
    checkScansStatus();
    
    // Set up interval to check status regularly
    const intervalId = setInterval(checkScansStatus, 5000);
    
    // Clean up interval on unmount or when dependencies change
    return () => clearInterval(intervalId);
  }, [activeScanIds]); // Only depend on activeScanIds to avoid too many refreshes

  const handleScanFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (scanFormData.disclaimer) {
      // Store data in localStorage in both cases for consistency
      localStorage.setItem('scanFormData', JSON.stringify(scanFormData));
      
      // If running scan now
      if (scanFormData.schedule === 'now') {
        // Use the initiateScan function
        initiateScan(scanFormData);
      } else {
        // Close the form and navigate to review page
        setShowScanForm(false);
        navigate('/assessment/review');
      }
    }
  };

  const renderStatus = (status: string) => {
    switch (status) {
      case 'completed':
        return <span className="text-success-600 dark:text-success-400">Completed</span>;
      case 'in-progress':
        return <span className="text-warning-600 dark:text-warning-400">In Progress</span>;
      default:
        return <span className="text-neutral-600 dark:text-neutral-300">{status}</span>;
    }
  };

  const toggleRowExpansion = (id: number) => {
    setExpandedRows(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  return (
    <div className="max-w-[1920px] mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Assessment Center</h1>
        <p className="text-neutral-600 dark:text-neutral-300">Launch security assessments and view scan results</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
        <motion.div
          className="bg-white dark:bg-[#202020] rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-5 cursor-pointer transition-all hover:shadow-md group"
          whileHover={{ y: -2 }}
          onClick={() => setShowScanForm(true)}
        >
          <div className="w-10 h-10 bg-primary-100 dark:bg-primary-900 rounded-lg flex items-center justify-center mb-3 group-hover:bg-primary-200 dark:group-hover:bg-primary-800 transition-colors">
            <Brain className="w-5 h-5 text-primary-600" />
          </div>
          <h3 className="text-lg font-semibold mb-2 group-hover:text-primary-600 transition-colors dark:text-white">
            Defendly AI-Powered Scan
          </h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-300">
            Run intelligent automated scans for vulnerabilities, misconfigurations, and missing security controls across web apps, APIs, IPs, and cloud assets.
          </p>
        </motion.div>

        <motion.div
          className="bg-white dark:bg-[#202020] rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-5 cursor-pointer transition-all hover:shadow-md group"
          whileHover={{ y: -2 }}
          onClick={() => navigate('/assessment/advanced')}
        >
          <div className="w-10 h-10 bg-secondary-100 dark:bg-secondary-900 rounded-lg flex items-center justify-center mb-3 group-hover:bg-secondary-200 dark:group-hover:bg-secondary-800 transition-colors">
            <FileSpreadsheet className="w-5 h-5 text-secondary-600" />
          </div>
          <h3 className="text-lg font-semibold mb-2 group-hover:text-secondary-600 transition-colors dark:text-white">
            Need Advanced Testing?
          </h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-300">
            Looking for advanced assessment pentesting, cloud config audits, or thick client assessments? Request our security experts now.
          </p>
        </motion.div>
      </div>

      <div className="bg-white dark:bg-[#202020] rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-5">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-lg font-semibold flex items-center gap-2 dark:text-neutral-100">
              Scans History
            </h2>
            <p className="text-sm text-neutral-600 dark:text-neutral-300 mt-1">
              View your latest scans and their security grades, issue counts, and compliance scores.
            </p>
          </div>
        </div>

        <div className="relative">
          <div className="overflow-x-auto">
            {recentScans.length > 0 ? (
              <table className="w-full">
                <thead className="sticky top-0 bg-white dark:bg-[#262626] z-10">
                  <tr className="border-b border-neutral-200 dark:border-neutral-800">
                    <th className="w-[40px]"></th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">Targets</th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">Organization</th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">Project</th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">Type</th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">Start</th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">End</th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">Status</th>
                    <th className="text-left py-2 px-3 text-xs font-medium text-neutral-600 dark:text-neutral-300">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentScans.map((scan) => (
                    <React.Fragment key={scan.id}>
                      <tr
                        className={`border-b border-neutral-100 dark:border-neutral-800 hover:bg-neutral-50 dark:hover:bg-[#262626] cursor-pointer ${
                          expandedRows.has(scan.id) ? 'bg-neutral-50 dark:bg-[#202020]' : ''
                        }`}
                        onClick={() => toggleRowExpansion(scan.id)}
                      >
                        <td className="w-[40px] text-center bg-inherit">
                          <motion.div
                            initial={false}
                            animate={{ rotate: expandedRows.has(scan.id) ? 180 : 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronDown className="w-4 h-4 inline-block dark:text-neutral-200" />
                          </motion.div>
                        </td>
                        <td className="py-2 px-3 bg-inherit">
                          <span className="font-mono text-xs text-neutral-800 dark:text-neutral-100">{scan.asset}</span>
                        </td>
                        <td className="py-2 px-3 text-sm bg-inherit text-neutral-800 dark:text-neutral-100">{scan.organization}</td>
                        <td className="py-2 px-3 text-sm bg-inherit text-neutral-800 dark:text-neutral-100">{scan.project}</td>
                        <td className="py-2 px-3 bg-inherit">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                            scan.type === 'AI Scan' 
                              ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-200'
                              : 'bg-secondary-100 text-secondary-800 dark:bg-secondary-900 dark:text-secondary-200'
                          }`}>
                            {scan.type}
                          </span>
                        </td>
                        <td className="py-2 px-3 text-sm bg-inherit text-neutral-800 dark:text-neutral-100">
                          <div className="flex flex-col">
                            <span>{scan.startDate}</span>
                            <span className="text-xs text-neutral-500 dark:text-neutral-400">{scan.startTime}</span>
                          </div>
                        </td>
                        <td className="py-2 px-3 text-sm bg-inherit text-neutral-800 dark:text-neutral-100">
                          <div className="flex flex-col">
                            <span>{scan.endDate}</span>
                            <span className="text-xs text-neutral-500 dark:text-neutral-400">{scan.endTime}</span>
                          </div>
                        </td>
                        <td className="py-2 px-3 text-sm bg-inherit text-neutral-800 dark:text-neutral-100">{renderStatus(scan.status)}</td>
                        <td className="py-2 px-3 bg-inherit">
                          <div className="relative dropdown-container">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setActiveDropdown(activeDropdown === scan.id ? null : scan.id);
                              }}
                              className="flex items-center gap-1 px-2 py-1 text-sm text-neutral-600 dark:text-neutral-200 hover:text-neutral-800 dark:hover:text-neutral-100 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded transition-colors"
                            >
                              Actions
                              <ChevronDown className="w-4 h-4" />
                            </button>
                            
                            <AnimatePresence>
                              {activeDropdown === scan.id && (
                                <motion.div
                                  initial={{ opacity: 0, y: -10 }}
                                  animate={{ opacity: 1, y: 0 }}
                                  exit={{ opacity: 0, y: -10 }}
                                  className="absolute right-0 mt-1 w-36 bg-white dark:bg-[#171717] rounded-md shadow-lg border border-neutral-200 dark:border-neutral-800 py-1 z-20"
                                >
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleActionClick(scan.id, 'run');
                                    }}
                                    className="w-full flex items-center gap-2 px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800"
                                  >
                                    <Play className="w-4 h-4" />
                                    Run Again
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleActionClick(scan.id, 'view');
                                    }}
                                    className="w-full flex items-center gap-2 px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800"
                                  >
                                    <Eye className="w-4 h-4" />
                                    View Report
                                  </button>
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleActionClick(scan.id, 'download');
                                    }}
                                    className="w-full flex items-center gap-2 px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-50 dark:hover:bg-neutral-800"
                                  >
                                    <Download className="w-4 h-4" />
                                    Download
                                  </button>
                                </motion.div>
                              )}
                            </AnimatePresence>
                          </div>
                        </td>
                      </tr>
                      <AnimatePresence>
                        {expandedRows.has(scan.id) && (
                          <motion.tr
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <td colSpan={9} className="bg-neutral-50 dark:bg-[#202020] p-0">
                              <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                transition={{ duration: 0.2, delay: 0.1 }}
                                className="p-4"
                              >
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                  <div>
                                    <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-200 mb-2">Description</h4>
                                    <p className="text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">{scan.description}</p>
                                  </div>
                                  <div className="space-y-4">
                                    <div>
                                      <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-200 mb-2">Scan Details</h4>
                                      <div className="grid grid-cols-2 gap-3">
                                        <div className="bg-white dark:bg-[#171717] p-3 rounded-lg border border-neutral-200 dark:border-neutral-800">
                                          <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                                            {scan.details.vulnerabilities}
                                          </div>
                                          <div className="text-xs text-neutral-600 dark:text-neutral-300">Vulnerabilities</div>
                                        </div>
                                        <div className="bg-white dark:bg-[#171717] p-3 rounded-lg border border-neutral-200 dark:border-neutral-800">
                                          <div className="text-2xl font-bold text-warning-600 dark:text-warning-400">
                                            {scan.details.endpoints}
                                          </div>
                                          <div className="text-xs text-neutral-600 dark:text-neutral-300">Endpoints</div>
                                        </div>
                                      </div>
                                    </div>
                                    <div>
                                      <h4 className="text-sm font-semibold text-neutral-700 dark:text-neutral-200 mb-2">Severity Breakdown</h4>
                                      <div className="grid grid-cols-3 gap-2">
                                        <div className="bg-danger-600 dark:bg-danger-600 p-2 rounded">
                                          <div className="text-lg font-bold text-white">{scan.details.severity.high}</div>
                                          <div className="text-xs text-white">High</div>
                                        </div>
                                        <div className="bg-warning-500 dark:bg-warning-500 p-2 rounded">
                                          <div className="text-lg font-bold text-white">{scan.details.severity.medium}</div>
                                          <div className="text-xs text-white">Medium</div>
                                        </div>
                                        <div className="bg-success-700 dark:bg-success-700 p-2 rounded">
                                          <div className="text-lg font-bold text-white">{scan.details.severity.low}</div>
                                          <div className="text-xs text-white">Low</div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </motion.div>
                            </td>
                          </motion.tr>
                        )}
                      </AnimatePresence>
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="flex flex-col items-center justify-center py-16">
                <AlertCircle className="w-12 h-12 text-neutral-300 mb-4" />
                <h3 className="text-lg font-semibold text-neutral-600 dark:text-neutral-300 mb-2">No Recent Scans</h3>
                <p className="text-sm text-neutral-500 dark:text-neutral-400">Start by running your first security assessment</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI Scan Form Modal */}
      <AnimatePresence>
        {showScanForm && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setShowScanForm(false)}
            />
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="fixed inset-x-0 top-[10%] mx-auto max-w-xl bg-white dark:bg-[#202020] rounded-lg shadow-xl z-50"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-center px-6 py-4 border-b border-neutral-200 dark:border-neutral-800">
                <h3 className="text-lg font-semibold text-neutral-800 dark:text-neutral-100">Start AI-Powered Scan</h3>
                <button 
                  onClick={() => setShowScanForm(false)}
                  className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-300 dark:hover:text-neutral-100 transition-colors"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              
              <form onSubmit={handleScanFormSubmit} className="p-6 space-y-4">
                <div className="flex justify-between items-center mb-2">
                  <div className="grid grid-cols-2 gap-4 flex-1">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                        Organization Name
                      </label>
                      <input
                        type="text"
                        value={scanFormData.organization}
                        onChange={(e) => setScanFormData({ ...scanFormData, organization: e.target.value })}
                        className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                        placeholder="Enter organization name"
                        required
                      />
                    </div>
                    <div>
                      <div className="flex justify-between items-center">
                        <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                          Target Name
                        </label>
                        
                      </div>
                      <input
                        type="text"
                        value={scanFormData.projectName}
                        onChange={(e) => setScanFormData({ ...scanFormData, projectName: e.target.value })}
                        className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                        placeholder="Enter project name"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Target Assets <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={scanFormData.targetAsset}
                    onChange={(e) => setScanFormData({ ...scanFormData, targetAsset: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                  >
                    <option value="web">Web Application</option>
                    <option value="api">API</option>
                    <option value="mobile">Mobile App</option>
                    <option value="cloud">Cloud Infrastructure</option>
                    <option value="network">Network</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Scan Target <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={scanFormData.scanTarget}
                    onChange={(e) => setScanFormData({ ...scanFormData, scanTarget: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                    placeholder="Enter URL, IP address, or domain"
                    required
                  />
                </div>

                {/* Auth Scan Toggle - now styled like schedule buttons and placed under Scan Target */}
                <div className="mb-2">
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-2">
                    Auth Scan?
                  </label>
                  <div className="flex gap-4">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        checked={authScan}
                        onChange={() => setAuthScan(true)}
                        className="w-4 h-4 text-primary-500 border-neutral-300 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-neutral-700 dark:text-neutral-200">Yes</span>
                    </label>
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="radio"
                        checked={!authScan}
                        onChange={() => setAuthScan(false)}
                        className="w-4 h-4 text-primary-500 border-neutral-300 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-neutral-700 dark:text-neutral-200">No</span>
                    </label>
                  </div>
                </div>

                {authScan && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                        Email ID <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        value={scanFormData.email}
                        onChange={e => setScanFormData({ ...scanFormData, email: e.target.value })}
                        className="w-full max-w-xs px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                        placeholder="Enter email/Username"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                        Password <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="password"
                        value={scanFormData.password}
                        onChange={e => setScanFormData({ ...scanFormData, password: e.target.value })}
                        className="w-full max-w-xs px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                        placeholder="Enter password"
                        required
                      />
                    </div>
                    <div className="col-span-2">
                      <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                        Login URL <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={scanFormData.loginUrl}
                        onChange={e => setScanFormData({ ...scanFormData, loginUrl: e.target.value })}
                        className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                        placeholder="Enter login page URL"
                        required
                      />
                    </div>
                  </div>
                )}

                {/* Restore Label field here */}
                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                    Label <span className="text-xs text-neutral-400">(optional)</span>
                  </label>
                  <input
                    type="text"
                    value={scanFormData.label}
                    onChange={e => setScanFormData({ ...scanFormData, label: e.target.value })}
                    className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                    placeholder="Add a label for this scan (e.g. 'Quarterly PCI', 'Staging')"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-2">
                    Schedule
                  </label>
                  <div className="flex gap-4 mb-3">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={scanFormData.schedule === 'now'}
                        onChange={() => setScanFormData({ ...scanFormData, schedule: 'now' })}
                        className="w-4 h-4 text-primary-500 border-neutral-300 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-neutral-700 dark:text-neutral-200">Now</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        checked={scanFormData.schedule === 'later'}
                        onChange={() => setScanFormData({ ...scanFormData, schedule: 'later' })}
                        className="w-4 h-4 text-primary-500 border-neutral-300 focus:ring-primary-500"
                      />
                      <span className="ml-2 text-sm text-neutral-700 dark:text-neutral-200">Later</span>
                    </label>
                  </div>

                  {scanFormData.schedule === 'later' && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                          Date
                        </label>
                        <div className="relative">
                          <input
                            type="date"
                            value={scanFormData.scheduledDate}
                            onChange={(e) => setScanFormData({ ...scanFormData, scheduledDate: e.target.value })}
                            className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                            min={new Date().toISOString().split('T')[0]}
                            required={scanFormData.schedule === 'later'}
                          />
                          <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400 pointer-events-none" />
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-200 mb-1">
                          Time
                        </label>
                        <div className="relative">
                          <input
                            type="time"
                            value={scanFormData.scheduledTime}
                            onChange={(e) => setScanFormData({ ...scanFormData, scheduledTime: e.target.value })}
                            className="w-full px-3 py-2 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white dark:bg-[#171717] text-neutral-900 dark:text-neutral-100"
                            required={scanFormData.schedule === 'later'}
                          />
                          <Clock className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400 pointer-events-none" />
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex items-start gap-2 pt-2">
                  <input
                    type="checkbox"
                    id="disclaimer"
                    checked={scanFormData.disclaimer}
                    onChange={(e) => setScanFormData({ ...scanFormData, disclaimer: e.target.checked })}
                    className="mt-1 w-4 h-4 text-primary-500 border-neutral-300 rounded focus:ring-primary-500"
                  />
                  <label htmlFor="disclaimer" className="text-sm text-neutral-600 dark:text-neutral-300">
                    I confirm that I own or have authorization to scan the specified assets and that the information provided is accurate.
                  </label>
                </div>

                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={!scanFormData.disclaimer}
                    className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-[#00457f] text-white rounded-md font-medium hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    Continue
                  </button>
                </div>
              </form>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AssessmentCenter;