import { Port } from '../types/types';

interface NmapPort {
  '@portid': string;
  '@protocol': string;
  state: {
    '@state': string;
    '@reason': string;
  };
  service: {
    '@name': string;
    '@product'?: string;
    '@version'?: string;
  };
}

interface NmapHost {
  status: {
    '@state': string;
  };
  address: {
    '@addr': string;
    '@addrtype': string;
  };
  ports: {
    port: NmapPort[];
  };
}

interface NmapScan {
  nmaprun: {
    host: NmapHost | NmapHost[];
  };
}

export const parseNmapData = (nmapData: NmapScan) => {
  const ports: Port[] = [];
  
  // Ensure hosts is an array
  const hosts = Array.isArray(nmapData.nmaprun.host) 
    ? nmapData.nmaprun.host 
    : [nmapData.nmaprun.host];
  
  // Process each host
  hosts.forEach((host, hostIndex) => {
    // Skip hosts that are not up
    if (host.status['@state'] !== 'up') {
      return;
    }
    
    // Ensure ports is an array
    const hostPorts = Array.isArray(host.ports.port) 
      ? host.ports.port 
      : [host.ports.port];
    
    // Process each port
    hostPorts.forEach((port, portIndex) => {
      // Skip ports that are not open
      if (port.state['@state'] !== 'open') {
        return;
      }
      
      // Determine risk level based on service
      let riskLevel: 'high' | 'medium' | 'low' = 'low';
      
      // This is a simplified risk assessment - a real implementation would be more comprehensive
      const serviceName = port.service['@name'].toLowerCase();
      
      if (['mysql', 'mssql', 'oracle', 'postgresql', 'mongodb', 'redis', 'ftp', 'telnet'].includes(serviceName)) {
        riskLevel = 'high';
      } else if (['ssh', 'smtp', 'imap', 'pop3', 'dns', 'snmp', 'ldap', 'vnc'].includes(serviceName)) {
        riskLevel = 'medium';
      }
      
      // Create port entry
      const portEntry: Port = {
        id: `nmap-${hostIndex}-${portIndex}`,
        port: parseInt(port['@portid'], 10),
        service: port.service['@name'],
        protocol: port['@protocol'],
        riskLevel,
        state: port.state['@state']
      };
      
      ports.push(portEntry);
    });
  });
  
  return {
    ports
  };
};