import { Vulnerability, Port, VulnerableEndpoint } from '../types/types';

export const mockVulnerabilities: Vulnerability[] = [
  {
    id: '1',
    name: 'SQL Injection in Login Form',
    severity: 'high',
    endpoint: '/api/auth/login',
    attackVector: 'Injection',
    description: 'The login form is vulnerable to SQL injection attacks, allowing attackers to bypass authentication or extract sensitive information from the database.',
    remediation: 'Use parameterized queries or prepared statements instead of concatenating user input directly into SQL queries. Implement proper input validation and sanitization.',
    complianceMapping: ['OWASP-A03', 'CIS-4.2']
  },
  {
    id: '2',
    name: 'Cross-Site Scripting (XSS) in Comment Section',
    severity: 'medium',
    endpoint: '/api/posts/comment',
    attackVector: 'XSS',
    description: 'The comment section allows insertion of malicious JavaScript, which can be executed in other users\' browsers when they view the comments.',
    remediation: 'Implement proper output encoding and CSP (Content Security Policy) headers. Use a library like DOMPurify to sanitize user input.',
    complianceMapping: ['OWASP-A07', 'CIS-5.1']
  },
  {
    id: '3',
    name: 'Improper Access Control',
    severity: 'high',
    endpoint: '/api/admin/users',
    attackVector: 'Authentication',
    description: 'Endpoints with sensitive admin functionality are accessible without proper authorization checks, allowing regular users to access admin features.',
    remediation: 'Implement proper authorization checks at all privileged endpoints. Use role-based access control (RBAC) and verify user permissions on both client and server side.',
    complianceMapping: ['OWASP-A01', 'CIS-4.1']
  },
  {
    id: '4',
    name: 'Insecure Direct Object Reference (IDOR)',
    severity: 'medium',
    endpoint: '/api/users/{id}/profile',
    attackVector: 'IDOR',
    description: 'The user profile endpoint allows accessing other user profiles by changing the ID parameter, revealing sensitive user information.',
    remediation: 'Implement proper authorization checks for each resource access. Use indirect references or verify the requesting user has permission to access the requested resource.',
    complianceMapping: ['OWASP-A01', 'CIS-4.3']
  },
  {
    id: '5',
    name: 'Missing HTTP Security Headers',
    severity: 'low',
    endpoint: '/',
    attackVector: 'Misconfiguration',
    description: 'The application is missing important security headers such as Content-Security-Policy, X-Content-Type-Options, X-Frame-Options.',
    remediation: 'Add appropriate security headers to HTTP responses, including CSP, X-Content-Type-Options, X-Frame-Options, and others.',
    complianceMapping: ['OWASP-A05', 'CIS-2.8']
  },
  {
    id: '6',
    name: 'Information Disclosure in Error Messages',
    severity: 'low',
    endpoint: '/api/search',
    attackVector: 'Information Disclosure',
    description: 'Detailed error messages reveal implementation details such as database structure, file paths, or server information.',
    remediation: 'Implement generic error messages in production. Log detailed errors server-side but return only generic messages to users.',
    complianceMapping: ['OWASP-A09', 'CIS-7.2']
  },
  {
    id: '7',
    name: 'Server-Side Request Forgery (SSRF)',
    severity: 'high',
    endpoint: '/api/fetch-resource',
    attackVector: 'SSRF',
    description: 'The application accepts URLs from users and makes server-side requests, allowing attackers to probe internal services or access sensitive resources.',
    remediation: 'Implement a whitelist of allowed domains and protocols. Use a URL parser to validate URLs and their components. Block requests to internal networks.',
    complianceMapping: ['OWASP-A10', 'CIS-12.1']
  },
  {
    id: '8',
    name: 'Insecure Deserialization',
    severity: 'medium',
    endpoint: '/api/import',
    attackVector: 'Deserialization',
    description: 'The application deserializes user-provided data without proper validation, potentially allowing arbitrary code execution.',
    remediation: 'Use safer data formats like JSON. If serialization is necessary, implement integrity checks and never deserialize untrusted data.',
    complianceMapping: ['OWASP-A08', 'CIS-16.2']
  },
  {
    id: '9',
    name: 'Weak Password Policy',
    severity: 'low',
    endpoint: '/api/auth/register',
    attackVector: 'Authentication',
    description: 'The application allows users to create accounts with weak passwords, increasing the risk of credential-based attacks.',
    remediation: 'Implement a strong password policy requiring minimum length, complexity, and checking against commonly used or breached passwords.',
    complianceMapping: ['OWASP-A07', 'CIS-4.4']
  },
  {
    id: '10',
    name: 'Outdated Dependencies',
    severity: 'medium',
    endpoint: '/',
    attackVector: 'Vulnerable Components',
    description: 'The application uses outdated libraries and components with known security vulnerabilities.',
    remediation: 'Regularly update dependencies and implement a software composition analysis (SCA) tool to detect vulnerable components.',
    complianceMapping: ['OWASP-A06', 'CIS-18.1']
  }
];

export const mockPorts: Port[] = [
  {
    id: '1',
    port: 22,
    service: 'SSH',
    protocol: 'tcp',
    riskLevel: 'medium',
    state: 'open'
  },
  {
    id: '2',
    port: 80,
    service: 'HTTP',
    protocol: 'tcp',
    riskLevel: 'low',
    state: 'open'
  },
  {
    id: '3',
    port: 443,
    service: 'HTTPS',
    protocol: 'tcp',
    riskLevel: 'low',
    state: 'open'
  },
  {
    id: '4',
    port: 3306,
    service: 'MySQL',
    protocol: 'tcp',
    riskLevel: 'high',
    state: 'open'
  },
  {
    id: '5',
    port: 25,
    service: 'SMTP',
    protocol: 'tcp',
    riskLevel: 'medium',
    state: 'open'
  },
  {
    id: '6',
    port: 21,
    service: 'FTP',
    protocol: 'tcp',
    riskLevel: 'high',
    state: 'open'
  },
  {
    id: '7',
    port: 8080,
    service: 'HTTP-Proxy',
    protocol: 'tcp',
    riskLevel: 'medium',
    state: 'open'
  },
  {
    id: '8',
    port: 1433,
    service: 'MS-SQL',
    protocol: 'tcp',
    riskLevel: 'high',
    state: 'open'
  }
];

export const mockEndpoints: VulnerableEndpoint[] = [
  {
    id: '1',
    path: '/api/auth/login',
    method: 'POST',
    vulnerabilityType: 'SQL Injection',
    severity: 'high'
  },
  {
    id: '2',
    path: '/api/users',
    method: 'GET',
    vulnerabilityType: 'Information Disclosure',
    severity: 'medium'
  },
  {
    id: '3',
    path: '/api/admin/panel',
    method: 'GET',
    vulnerabilityType: 'Authorization Bypass',
    severity: 'high'
  },
  {
    id: '4',
    path: '/api/posts/comment',
    method: 'POST',
    vulnerabilityType: 'XSS',
    severity: 'medium'
  },
  {
    id: '5',
    path: '/api/search',
    method: 'GET',
    vulnerabilityType: 'SQL Injection',
    severity: 'high'
  },
  {
    id: '6',
    path: '/api/file/upload',
    method: 'POST',
    vulnerabilityType: 'File Upload',
    severity: 'high'
  },
  {
    id: '7',
    path: '/api/user/profile',
    method: 'PUT',
    vulnerabilityType: 'CSRF',
    severity: 'medium'
  },
  {
    id: '8',
    path: '/api/products',
    method: 'GET',
    vulnerabilityType: 'Information Leakage',
    severity: 'low'
  }
];

export const mockSubdomains = [
  { id: '1', name: 'api.example.com', exposure: 'high', type: 'API Gateway' },
  { id: '2', name: 'admin.example.com', exposure: 'high', type: 'Admin Panel' },
  { id: '3', name: 'dev.example.com', exposure: 'medium', type: 'Development' }
];

import { SecurityReport } from '../types';

export const mockSecurityReport: SecurityReport = {
  cyberHygieneScore: {
    score: 76,
    grade: 'B',
    previousScore: 70,
    trend: 'up'
  },
  vendorRiskRating: {
    grade: 'B',
    score: 78,
    recommendation: 'Monitor'
  },
  attackSurfaceIndex: {
    publicIps: 12,
    subdomains: 3,
    openPorts: 43,
    exposedServices: 8
  },
  complianceReadiness: {
    cis: {
      percentage: 83,
      findings: [
        { category: 'Account Management', count: 3 },
        { category: 'Audit Logging', count: 2 },
        { category: 'Data Protection', count: 4 },
        { category: 'Network Defense', count: 1 }
      ]
    },
    owasp: {
      percentage: 71,
      findings: [
        { category: 'Injection', count: 2 },
        { category: 'Broken Authentication', count: 3 },
        { category: 'Sensitive Data Exposure', count: 5 },
        { category: 'XML External Entities', count: 1 }
      ]
    }
  },
  vulnerabilities: [
    {
      id: 'CVE-2023-1234',
      name: 'SQL Injection in Login Form',
      severity: 'Critical',
      pluginId: 'SQL-INJ-001',
      uri: '/api/auth/login',
      solution: 'Use parameterized queries and input validation',
      references: ['https://owasp.org/www-community/attacks/SQL_Injection'],
      details: {
        cwe: 'CWE-89',
        evidence: 'POST /api/auth/login with parameter: username=admin\'--',
        cvss: 9.8,
        description: 'The login endpoint is vulnerable to SQL injection attacks, allowing attackers to bypass authentication.'
      }
    },
    {
      id: 'CVE-2023-5678',
      name: 'Cross-Site Scripting in Comment Field',
      severity: 'High',
      pluginId: 'XSS-002',
      uri: '/api/comments',
      solution: 'Implement proper output encoding and CSP headers',
      references: ['https://owasp.org/www-community/attacks/xss/'],
      details: {
        cwe: 'CWE-79',
        evidence: 'POST /api/comments with body: {"comment": "<script>alert(1)</script>"}',
        cvss: 7.6,
        description: 'User-supplied input in comment fields is not properly sanitized, allowing XSS attacks.'
      }
    },
    {
      id: 'CVE-2023-9012',
      name: 'Outdated TLS Version',
      severity: 'Medium',
      pluginId: 'TLS-003',
      uri: 'https://api.example.com',
      solution: 'Update server configuration to use TLS 1.3 and disable older protocols',
      references: ['https://www.acunetix.com/blog/articles/tls-vulnerabilities-attacks-final-part/'],
      details: {
        cwe: 'CWE-327',
        evidence: 'Server supports TLS 1.0 and TLS 1.1',
        cvss: 5.2,
        description: 'The API server supports deprecated TLS versions which are known to have security vulnerabilities.'
      }
    },
    {
      id: 'CVE-2023-3456',
      name: 'Missing Rate Limiting',
      severity: 'Medium',
      pluginId: 'SEC-004',
      uri: '/api/password-reset',
      solution: 'Implement rate limiting on all authentication endpoints',
      references: ['https://cheatsheetseries.owasp.org/cheatsheets/Authentication_Cheat_Sheet.html'],
      details: {
        cwe: 'CWE-307',
        evidence: 'Sent 100 password reset requests in 60 seconds without blocking',
        cvss: 5.3,
        description: 'The password reset endpoint does not implement rate limiting, making it vulnerable to brute force attacks.'
      }
    },
    {
      id: 'CVE-2023-7890',
      name: 'Information Disclosure in Error Messages',
      severity: 'Low',
      pluginId: 'INFO-005',
      uri: '/api/users',
      solution: 'Implement generic error messages in production',
      references: ['https://www.owasp.org/index.php/Information_Leakage'],
      details: {
        cwe: 'CWE-209',
        evidence: 'Error response includes stack trace and database details',
        cvss: 3.1,
        description: 'Detailed error messages reveal sensitive information about the application architecture and database schema.'
      }
    }
  ],
  misconfigurations: [
    {
      id: 'MISC-001',
      type: 'Insecure Cookies',
      description: 'Session cookies are missing Secure and HttpOnly flags',
      impact: 'Cookies may be accessible via client-side scripts or transmitted over unencrypted connections',
      recommendation: 'Set Secure and HttpOnly flags for all sensitive cookies'
    },
    {
      id: 'MISC-002',
      type: 'Missing Security Headers',
      description: 'Content-Security-Policy header is not implemented',
      impact: 'Application is vulnerable to XSS and other injection attacks',
      recommendation: 'Implement a strict CSP policy to mitigate client-side injection risks'
    },
    {
      id: 'MISC-003',
      type: 'Unsafe CORS Configuration',
      description: 'CORS allows requests from any origin with credentials',
      impact: 'Potentially allows malicious sites to make authenticated requests',
      recommendation: 'Restrict CORS to specific trusted origins and avoid using wildcard with credentials'
    }
  ],
  exposedAssets: [
    {
      id: 'EXP-001',
      type: 'Public',
      name: 'Admin portal',
      source: 'Shodan',
      discoveryDate: '2023-03-15',
      risk: 'High'
    },
    {
      id: 'EXP-002',
      type: 'Public',
      name: 'API documentation',
      source: 'Google',
      discoveryDate: '2023-02-28',
      risk: 'Medium'
    },
    {
      id: 'EXP-003',
      type: 'Shadow IT',
      name: 'Unauthorized cloud storage',
      source: 'Manual discovery',
      discoveryDate: '2023-04-10',
      risk: 'High'
    }
  ],
  aiRecommendations: [
    {
      issue: 'SQL Injection in Login Form',
      fix: 'Use parameterized queries to prevent SQL injection',
      code: `
// Unsafe code:
const query = "SELECT * FROM users WHERE username='" + username + "' AND password='" + password + "'";

// Secure code:
const query = "SELECT * FROM users WHERE username=? AND password=?";
const params = [username, password];`,
      owaspTag: 'A1:2021-Injection'
    },
    {
      issue: 'Cross-Site Scripting in Comment Field',
      fix: 'Sanitize user input and implement CSP headers',
      code: `
// Unsafe code:
element.innerHTML = userComment;

// Secure code:
import DOMPurify from 'dompurify';
element.innerHTML = DOMPurify.sanitize(userComment);`,
      owaspTag: 'A7:2021-XSS'
    }
  ]
};