// Vulnerability Types
export interface Vulnerability {
  id: string;
  name: string;
  severity: string;
  endpoint: string;
  attackVector: string;
  description: string;
  complianceMapping: string[];
  evidence?: string[]; // <-- Add this line
  solutions?: string[];
}
  


// Port Types
export interface Port {
  id: string;
  port: number;
  service: string;
  protocol: string;
  riskLevel: 'high' | 'medium' | 'low';
  state: string;
}

// Vulnerable Endpoint Types
export interface VulnerableEndpoint {
  id: string;
  path: string;
  method: string;
  vulnerabilityType: string;
  severity: 'high' | 'medium' | 'low' | 'info';
}

// TLS Security Types
export interface TLSSecurity {
  valid: boolean;
  expiry: string;
  protocols: {
    name: string;
    supported: boolean;
    secure: boolean;
  }[];
  score: number;
}

// Public Asset Types
export interface PublicAsset {
  id: string;
  url: string;
  type: string;
  exposure: 'high' | 'medium' | 'low';
  description: string;
}

// Exploit Types
export interface Exploit {
  id: string;
  cve: string;
  exploitDbId: string;
  description: string;
  availability: boolean;
}

// Dashboard Stats
export interface DashboardStats {
  cyberHygieneScore: number;
  vendorRiskRating: number;
  vulnerabilitiesByType: {
    type: string;
    count: number;
  }[];
  vulnerabilitiesBySeverity: {
    severity: string;
    count: number;
  }[];
  openPortsByRisk: {
    risk: string;
    count: number;
  }[];
  tlsSecurityScore: number;
  complianceCoverage: {
    framework: string;
    covered: number;
    total: number;
  }[];
}

export interface ScanFile {
  id: string;
  name: string;
  type: 'zap' | 'nmap';
  date: string;
  summary: {
    vulnerabilities: number;
    highSeverity: number;
    openPorts: number;
    endpoints: number;
  };
}

// NLP Query Result
export interface NLPQueryResult {
  type: 'vulnerabilities' | 'endpoints' | 'ports' | 'reports';
  data: Vulnerability[] | VulnerableEndpoint[] | Port[] | ScanFile[];
  message: string;
}

// News API interfaces
export interface NewsArticle {
  title: string;
  description: string;
  url?: string;
  link?: string;
  href?: string;
  urlToImage?: string;
  publishedAt: string;
  source: {
    id?: string;
    name: string;
    url?: string;
  };
}

export interface NewsApiResponse {
  status: string;
  totalResults: number;
  articles: NewsArticle[];
}

// API Response interfaces based on the new structure
export interface ApiAlertInstance {
  attack?: string;
  evidence?: string;
  id?: string;
  method?: string;
  otherinfo?: string;
  param?: string;
  uri?: string;
}

export interface ApiAlert {
  alert?: string;
  alertRef?: string;
  confidence?: string;
  count?: string;
  cweid?: string;
  desc?: string;
  instances?: ApiAlertInstance[];
  name?: string;
  otherinfo?: string;
  pluginid?: string;
  reference?: string;
  riskcode?: string;
  riskdesc?: string;
  solution?: string;
  sourceid?: string;
  wascid?: string;
}

export interface AttackSurfaceMetrics {
  detected_subdomains?: string[];
  exposed_services?: string[];
  exposed_services_count?: number;
  open_ports?: number[];
  open_ports_count?: number;
  public_ips?: string[];
  public_ips_count?: number;
  subdomains_count?: number;
}

export interface AttackSurfaceIndex {
  metrics?: AttackSurfaceMetrics;
  score?: number;
}

export interface CISFindings {
  [key: string]: number;
}

export interface OWASPFindings {
  [key: string]: number;
}

export interface ComplianceReadiness {
  cis_compliance?: number;
  cis_findings?: CISFindings;
  overall_compliance_score?: number;
  owasp_compliance?: number;
  owasp_findings?: OWASPFindings;
  total_cis_issues?: number;
  total_owasp_issues?: number;
}

export interface CyberHygieneMetrics {
  critical_issues?: number;
  high_issues?: number;
  low_issues?: number;
  medium_issues?: number;
  missing_security_headers?: boolean;
}

export interface CyberHygieneScore {
  grade?: string;
  metrics?: CyberHygieneMetrics;
  score?: number;
}

export interface EndpointsData {
  endpoints?: string[];
  total_count?: number;
}

export interface SecurityMisconfigurations {
  insecure_cookies?: number;
  missing_security_headers?: {
    count?: number;
    headers?: string[];
  };
  other_misconfigurations?: number;
  total_misconfigurations?: number;
  unsafe_cors_configurations?: number;
}

export interface ReputationAnalysisStats {
  harmless?: number;
  malicious?: number;
  suspicious?: number;
  total_engines?: number;
  undetected?: number;
}

export interface ReputationData {
  analysis_stats?: ReputationAnalysisStats;
  first_seen?: string | null;
  last_analysis?: string;
  reputation_score?: number;
  risk_assessment?: string;
  status?: string;
  threat_score?: number;
}

export interface ThreatIntelligenceScoreData {
  assessment?: string;
  confidence?: string;
  score?: number;
}

export interface ThreatIntelligence {
  reputation_data?: ReputationData;
  threat_intelligence_score?: ThreatIntelligenceScoreData;
}

export interface VendorRiskComponents {
  access_scope?: number;
  compliance_readiness?: number;
  data_sensitivity?: number;
  remediation_behavior?: number;
  security_testing?: number;
  threat_intelligence?: number;
}

export interface VendorRiskRating {
  components?: VendorRiskComponents;
  letter_grade?: string;
  numeric_rating?: number;
  recommendation?: string;
  risk_level?: string;
  weighted_score?: number;
}

export interface ApiScanData {
  _id?: string;
  scanDate?: string;
  status?: string;
  alerts?: ApiAlert[];
  url?: string;
  createdAt?: string;
  updatedAt?: string;
  __v?: number;
  attack_surface_index?: AttackSurfaceIndex;
  combined_security_score?: number;
  compliance_readiness?: ComplianceReadiness;
  cyber_hygiene_score?: CyberHygieneScore;
  endpoints?: EndpointsData;
  scan_date?: string;
  scan_id?: string;
  security_misconfigurations?: SecurityMisconfigurations;
  threat_intelligence?: ThreatIntelligence;
  vendor_risk_rating?: VendorRiskRating;
}

export interface ScanRequest {
  url: string;
  scanType: 'quick' | 'comprehensive';
  includeSubdomains: boolean;
}

// Compliance Framework
export interface ComplianceFramework {
  id: string;
  name: string;
  controls: {
    id: string;
    name: string;
    covered: boolean;
    relatedVulnerabilities: number;
  }[];
}