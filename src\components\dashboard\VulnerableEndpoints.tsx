
import React, { useState, useEffect } from 'react';
import { Info, ArrowRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useDashboard } from '../../context/DashboardContext';

interface VulnerableEndpoint {
  id: string;
  path: string;
  method: string;
  vulnerabilityType: string;
  severity: string;
}

interface VulnerableEndpointsProps {
  endpoints?: VulnerableEndpoint[];
  showAll?: boolean;
}

const VulnerableEndpoints: React.FC<VulnerableEndpointsProps> = ({ endpoints: propEndpoints, showAll = false }) => {
  const navigate = useNavigate();
  const { endpoints: contextEndpoints } = useDashboard();
  const [endpoints, setEndpoints] = useState<VulnerableEndpoint[]>([]);
  
  useEffect(() => {
    // Use props if provided, otherwise use context data
    if (propEndpoints && propEndpoints.length > 0) {
      setEndpoints(propEndpoints);
    } else if (contextEndpoints && contextEndpoints.length > 0) {
      setEndpoints(contextEndpoints);
    }
  }, [propEndpoints, contextEndpoints]);

  const renderMethodBadge = (method: string) => {
    let bgColor = 'bg-neutral-100 dark:bg-neutral-800';
    let textColor = 'text-neutral-800 dark:text-neutral-100';
    
    switch (method.toUpperCase()) {
      case 'GET':
        bgColor = 'bg-primary-100 dark:bg-primary-900';
        textColor = 'text-primary-800 dark:text-primary-200';
        break;
      case 'POST':
        bgColor = 'bg-success-100 dark:bg-success-900';
        textColor = 'text-success-800 dark:text-success-200';
        break;
      case 'PUT':
        bgColor = 'bg-warning-100 dark:bg-warning-900';
        textColor = 'text-warning-800 dark:text-warning-200';
        break;
      case 'DELETE':
        bgColor = 'bg-danger-100 dark:bg-danger-900';
        textColor = 'text-danger-800 dark:text-danger-200';
        break;
    }
    
    return (
      <span className={`${bgColor} ${textColor} text-xs font-medium px-2 py-0.5 rounded`}>
        {method.toUpperCase()}
      </span>
    );
  };

  const displayedEndpoints = showAll ? endpoints : endpoints.slice(0, 5);
  
  return (
    <div className="bg-white dark:bg-[#202020] rounded-[10px] p-6 border border-neutral-200 dark:border-[#232323] h-full flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-1 mr-3">
          <h3 className="text-lg font-medium text-neutral-900 dark:text-white">Vulnerable Endpoints</h3>
          <div className="tooltip-container relative group">
            <Info className="w-4 h-4 text-neutral-500 dark:text-neutral-400 cursor-help" />
            <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
              API endpoints and web paths that have been identified as vulnerable to security attacks. Each endpoint shows the HTTP method, path, and vulnerability type.
            </div>
          </div>
        </div>
        {/* <button className="bg-transparent border border-neutral-400 dark:border-neutral-600 rounded-md px-2 py-1 text-xs text-neutral-700 dark:text-neutral-300 hover:border-neutral-500 dark:hover:border-neutral-500 transition-colors">
          View Details
        </button> */}
      </div>
      
      <div className="overflow-y-auto flex-1">
        <div className="space-y-2">
          {displayedEndpoints.map((endpoint) => (
            <div
              key={endpoint.id}
              className={`p-3 bg-white dark:bg-neutral-900 rounded-md shadow-sm border-l-4 hover:shadow-md transition-shadow ${
                endpoint.severity === 'high'
                  ? 'border-l-danger-500'
                  : endpoint.severity === 'medium'
                    ? 'border-l-warning-500'
                    : 'border-l-success-500'
              }`}
            >
              <div className="flex justify-between items-start">
                <div className="overflow-hidden">
                  <div className="flex items-center mb-1">
                    {renderMethodBadge(endpoint.method)}
                    <span className="ml-2 font-mono text-sm text-neutral-800 dark:text-neutral-100 overflow-hidden text-ellipsis">
                      {endpoint.path}
                    </span>
                  </div>
                  <div className="text-xs text-neutral-600 dark:text-neutral-300">
                    <span className="font-medium">Vulnerability:</span> {endpoint.vulnerabilityType}
                  </div>
                </div>
                <span className={`severity-badge severity-${endpoint.severity} ml-2`}>
                  {endpoint.severity.charAt(0).toUpperCase() + endpoint.severity.slice(1)}
                </span>
              </div>
            </div>
          ))}
          
          {endpoints.length === 0 && (
            <div className="text-center py-6 text-neutral-500 dark:text-neutral-300">
              No vulnerable endpoints found
            </div>
          )}
        </div>
      </div>

      {!showAll && endpoints.length > 5 && (
        <div className="mt-4 flex justify-center border-t border-neutral-100 dark:border-neutral-800 pt-4">
          <button
            onClick={() => navigate('/vulnerabilities?tab=endpoints')}
            className="flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm"
          >
            Show All
            <ArrowRight className="w-4 h-4 ml-1" />
          </button>
        </div>
      )}
    </div>
  );
};

export default VulnerableEndpoints;