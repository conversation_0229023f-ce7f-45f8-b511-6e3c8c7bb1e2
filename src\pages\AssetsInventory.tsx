import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import assetData from './assetsinventoryjson.json';
import jsPDF from 'jspdf';

// Helper for formatting date/time
const formatDate = (dateStr: string) =>
  dateStr ? new Date(dateStr).toLocaleString() : '-';

// Helper for CVSS color and Threat color (matching)
const getCvssColor = (score: number) => {
  if (score > 7.5) return 'bg-[#7f1d1d]  text-white'; // Critical
  if (score > 5.0) return 'bg-[#dc2626] text-white'; // High
  if (score > 2.5) return 'bg-[#fbbf24] text-black'; // Medium
  if (score > 0) return 'bg-[#16a34a] text-white'; // Low
  return 'bg-gray-300 text-black';
};

const getThreatColor = (threat: string) => {
  switch (threat?.toLowerCase()) {
    case 'critical': return 'text-[#7f1d1d] dark:text-[#ff0000] font-bold';
    case 'high': return 'text-[#dc2626] font-bold';
    case 'medium': return 'text-[#fbbf24] font-bold';
    case 'low': return 'text-[#16a34a] font-bold';
    default: return 'text-neutral-700';
  }
};

// Helper for Threat level from CVSS
const getThreatLevelFromCvss = (cvss: number): 'Critical' | 'High' | 'Medium' | 'Low' => {
  if (cvss > 7.5) return 'Critical';
  if (cvss > 5.0) return 'High';
  if (cvss > 2.5) return 'Medium';
  return 'Low';
};

// Helper for CVE badge
const CveBadge = ({ cve }: { cve: string }) => (
  <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded mr-1 mb-1">{cve}</span>
);

// Get results array from JSON
const results: any[] = assetData?.report?.report?.results?.result || [];

const AssetsInventory: React.FC = () => {
  const navigate = useNavigate();
  const scanInfo = assetData.report || {};
  const [expanded, setExpanded] = useState<number | null>(null);

  // Filter states
  const [severityFilter, setSeverityFilter] = useState<string>('All');
  const [cvssMin, setCvssMin] = useState<number>(0);
  const [cvssMax, setCvssMax] = useState<number>(10);
  const [portFilter, setPortFilter] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('All');
  const [search, setSearch] = useState<string>('');

  // Extract Hostname (Asset URL)
  // Show full host name if available in Hostname Determination Reporting or similar
  let hostname =
    scanInfo.report?.task?.target?.name ||
    scanInfo.host?.ip ||
    '-';

  // Try to extract full hostname from results (e.g., "sendy.steelmint.in")
  if (scanInfo.report?.results?.result) {
    const hostResult = scanInfo.report.results.result.find(
      (r: any) =>
        r.name &&
        r.name.toLowerCase().includes('hostname determination') &&
        r.description &&
        r.description.match(/Hostname\|Source\n([^\|]+)/)
    );
    if (hostResult) {
      const match = hostResult.description.match(/Hostname\|Source\n([^\|]+)/);
      if (match && match[1]) {
        hostname = match[1].trim();
      }
    }
  }

  const creationTime = scanInfo.creation_time || '-';
  const modificationTime = scanInfo.modification_time || '-';

  // Extract summary stats for tiles
  const vulnCount = Number(scanInfo.report?.vulns?.count || 0);
  const highVulnCount = Number(scanInfo.report?.result_count?.high?.full || 0);
  const openPorts = Number(scanInfo.report?.ports?.count || 0);
  const appCount = Number(scanInfo.report?.apps?.count || 0);

  // Table columns as per requirements
  const columns = [
    // { key: 'name', label: 'Name' }, // Removed as per request
    { key: 'host', label: 'Host' },
    { key: 'port', label: 'Port' },
    { key: 'severity', label: 'CVSS Score' },
    { key: 'threat', label: 'Threat Level' },
    { key: 'affected_version', label: 'Affected Version' },
    { key: 'solution', label: 'Solution' },
    { key: 'cves', label: 'CVEs' },
    { key: 'details', label: 'Details' }
  ];

  // Extract CVEs from description or nvt if available
  const extractCVEs = (result: any) => {
    // Try to extract CVEs from description or nvt (customize as per your data)
    const desc = result.description || '';
    const cveRegex = /(CVE-\d{4}-\d{4,7})/g;
    const matches = desc.match(cveRegex) || [];
    // If nvt._oid is a CVE, add it
    if (result.nvt && typeof result.nvt._oid === 'string' && result.nvt._oid.startsWith('CVE-')) {
      matches.push(result.nvt._oid);
    }
    // Remove duplicates
    return Array.from(new Set(matches));
  };

  // Extract affected version from description (customize as per your data)
  const extractAffectedVersion = (result: any) => {
    const desc = result.description || '';
    const match = desc.match(/Installed version:\s*([^\n]+)/i);
    return match ? match[1].trim() : '-';
  };

  // Extract solution (if available, else show "-")
  const extractSolution = (result: any) => {
    // If you have a solution field, use it, else try to parse from description
    if (result.solution) return result.solution;
    const desc = result.description || '';
    const match = desc.match(/Fixed version:\s*([^\n]+)/i);
    if (match) return `Update to ${match[1].trim()}`;
    return '-';
  };

  // Render details modal or expandable row
  const renderDetails = (result: any, idx: number) => (
    expanded === idx && (
      <tr>
        <td colSpan={columns.length} className="bg-neutral-50 dark:bg-neutral-800 p-4">
          <div className="text-sm mb-2">
            <span className="font-semibold">Full Description:</span>
            <pre className="whitespace-pre-wrap break-all text-xs bg-neutral-100 dark:bg-neutral-900 rounded p-2 mt-1">{result.description || '-'}</pre>
          </div>
          <div className="text-sm mb-2">
            <span className="font-semibold">Detection Details:</span>
            <pre className="whitespace-pre-wrap break-all text-xs bg-neutral-100 dark:bg-neutral-900 rounded p-2 mt-1">{JSON.stringify(result.detection || {}, null, 2)}</pre>
          </div>
          <div className="text-sm mb-2">
            <span className="font-semibold">External References:</span>
            <pre className="whitespace-pre-wrap break-all text-xs bg-neutral-100 dark:bg-neutral-900 rounded p-2 mt-1">{JSON.stringify(result.nvt || {}, null, 2)}</pre>
          </div>
          <div className="text-sm mb-2">
            <span className="font-semibold">All Data:</span>
            <pre className="whitespace-pre-wrap break-all text-xs bg-neutral-100 dark:bg-neutral-900 rounded p-2 mt-1">{JSON.stringify(result, null, 2)}</pre>
          </div>
        </td>
      </tr>
    )
  );

  // Get all unique ports for dropdown
  const allPorts = Array.from(
    new Set(results.map(r => r.port).filter(Boolean))
  );

  // Get all unique statuses if available
  const allStatuses = Array.from(
    new Set(results.map(r => r.status).filter(Boolean))
  );

  // Filtering logic
  const filteredResults = results.filter((result) => {
    // CVSS
    const cvss = parseFloat(result.severity || '0');
    // Threat level (computed from CVSS)
    const computedThreat = getThreatLevelFromCvss(cvss);
    // Severity filter
    if (severityFilter !== 'All' && computedThreat.toLowerCase() !== severityFilter.toLowerCase()) {
      return false;
    }
    if (cvss < cvssMin || cvss > cvssMax) {
      return false;
    }
    // Port
    if (portFilter.length > 0 && !portFilter.includes(result.port)) {
      return false;
    }
    // Status
    if (statusFilter !== 'All' && result.status && result.status !== statusFilter) {
      return false;
    }
    // Search
    if (search) {
      const s = search.toLowerCase();
      const name = (result.name || '').toLowerCase();
      const cves = (extractCVEs(result) || []).join(' ').toLowerCase();
      const solution = (extractSolution(result) || '').toLowerCase();
      if (!name.includes(s) && !cves.includes(s) && !solution.includes(s)) {
        return false;
      }
    }
    return true;
  });

  // Pagination state
  const [page, setPage] = useState(1);
  const rowsPerPage = 10;
  const totalPages = Math.ceil(filteredResults.length / rowsPerPage);

  // Paginated results
  const paginatedResults = filteredResults.slice(
    (page - 1) * rowsPerPage,
    page * rowsPerPage
  );

  // Reset filter handler
  const handleResetFilters = () => {
    setSeverityFilter('All');
    setCvssMin(0);
    setCvssMax(10);
    setPortFilter([]);
    setStatusFilter('All');
    setSearch('');
    setPage(1);
  };

  // Export as CSV
  const handleExportCSV = () => {
    const headers = columns.map(col => col.label).join(',');
    const rows = filteredResults.map(result => {
      const cves = (extractCVEs(result) || []).join(' ');
      return [
        result.host?.__text || result.host || '-',
        result.port || '-',
        result.severity || '-',
        result.threat || '-',
        extractAffectedVersion(result),
        extractSolution(result),
        cves,
        '' // Details column left empty
      ].map(val => `"${String(val).replace(/"/g, '""')}"`).join(',');
    });
    const csvContent = [headers, ...rows].join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'assets_inventory.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <div className="max-w-[1920px] mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-6">
      {/* Page Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-neutral-800 dark:text-neutral-100 mb-2">
          Assets Inventory
        </h1>
        <p className="text-neutral-600 dark:text-neutral-300 mb-4">
          Comprehensive report of all detected assets and their security findings.
        </p>
        <div className="flex flex-col md:flex-row md:items-center gap-4 bg-white dark:bg-[#202020] rounded-lg shadow border border-neutral-100 dark:border-neutral-800 p-6 mb-2 min-h-[120px]">
          <div className="flex-1">
            <div className="text-sm text-neutral-500">Host Target</div>
            {hostname && hostname !== '-' ? (
              <a
                href={
                  hostname.startsWith('http')
                    ? hostname
                    : `https://${hostname}`
                }
                target="_blank"
                rel="noopener noreferrer"
                className="font-semibold text-blue-700 dark:text-blue-400 underline break-all"
              >
                {hostname}
              </a>
            ) : (
              <div className="font-semibold break-all">-</div>
            )}
          </div>
          <div className="flex-1">
            <div className="text-sm text-neutral-500">Creation Time</div>
            <div className="font-semibold">{formatDate(creationTime)}</div>
          </div>
          <div className="flex-1">
            <div className="text-sm text-neutral-500">Modification Time</div>
            <div className="font-semibold">{formatDate(modificationTime)}</div>
          </div>
          {/* Tiles */}
          <div className="flex-1">
            <div className="grid grid-cols-2 grid-rows-2 gap-3 w-[420px]">
              <div className="rounded-lg p-3 flex flex-col items-center justify-center shadow-sm border-2 border-blue-400 dark:border-blue-400">
                <div className="text-xs  dark:text-blue-200 font-medium mb-1">Total Vulnerabilities</div>
                <div className="text-xl font-bold text-blue-800 dark:text-blue-100">{vulnCount}</div>
              </div>
              <div className="rounded-lg p-3 flex flex-col items-center justify-center shadow-sm border-2 border-red-400 dark:border-red-400">
                <div className="text-xs  dark:text-red-200 font-medium mb-1">High Vulnerabilities</div>
                <div className="text-xl font-bold text-red-800 dark:text-red-100">{highVulnCount}</div>
              </div>
              <div className="rounded-lg p-3 flex flex-col items-center justify-center shadow-sm border-2 border-green-400 dark:border-green-400">
                <div className="text-xs  dark:text-green-200 font-medium mb-1">Open Ports</div>
                <div className="text-xl font-bold text-green-800 dark:text-green-100">{openPorts}</div>
              </div>
              <div className="rounded-lg p-3 flex flex-col items-center justify-center shadow-sm border-2 border-yellow-400 dark:border-yellow-400">
                <div className="text-xs  dark:text-yellow-200 font-medium mb-1">Applications</div>
                <div className="text-xl font-bold text-yellow-800 dark:text-yellow-100">{appCount}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4 mb-4 items-end">
        {/* Severity */}
        <div>
          <label className="block text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">Severity</label>
          <select
            className="border rounded px-2 py-1 text-sm dark:bg-neutral-900 dark:text-neutral-100"
            value={severityFilter}
            onChange={e => setSeverityFilter(e.target.value)}
          >
            <option value="All">All</option>
            <option value="Critical">Critical</option>
            <option value="High">High</option>
            <option value="Medium">Medium</option>
            <option value="Low">Low</option>
          </select>
        </div>
        {/* CVSS Range */}
        <div>
          <label className="block text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">CVSS Range</label>
          <div className="flex items-center gap-2">
            <input
              type="number"
              min={0}
              max={10}
              step={0.1}
              value={cvssMin}
              onChange={e => setCvssMin(Number(e.target.value))}
              className="w-14 border rounded px-1 py-1 text-sm dark:bg-neutral-900 dark:text-neutral-100"
            />
            <span className="text-neutral-400">–</span>
            <input
              type="number"
              min={0}
              max={10}
              step={0.1}
              value={cvssMax}
              onChange={e => setCvssMax(Number(e.target.value))}
              className="w-14 border rounded px-1 py-1 text-sm dark:bg-neutral-900 dark:text-neutral-100"
            />
          </div>
        </div>
        {/* Port Dropdown */}
        <div>
          <label className="block text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">Port</label>
          <select
            value={portFilter[0] || 'All'}
            onChange={e => setPortFilter(e.target.value === 'All' ? [] : [e.target.value])}
            className="border rounded px-2 py-1 text-sm dark:bg-neutral-900 dark:text-neutral-100 min-w-[80px]"
          >
            <option value="All">All</option>
            {allPorts.map(port => (
              <option key={port} value={port}>{port}</option>
            ))}
          </select>
        </div>
        {/* Status */}
        {allStatuses.length > 0 && (
          <div>
            <label className="block text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">Status</label>
            <select
              className="border rounded px-2 py-1 text-sm dark:bg-neutral-900 dark:text-neutral-100"
              value={statusFilter}
              onChange={e => setStatusFilter(e.target.value)}
            >
              <option value="All">All</option>
              {allStatuses.map(status => (
                <option key={status} value={status}>{status}</option>
              ))}
            </select>
          </div>
        )}
        {/* Search */}
        <div className="flex-1 min-w-[180px] max-w-[300px]">
          <label className="block text-xs font-medium text-neutral-600 dark:text-neutral-300 mb-1">Search</label>
          <input
            type="text"
            placeholder="Search by name, CVE, or solution"
            value={search}
            onChange={e => setSearch(e.target.value)}
            className="border rounded px-2 py-1 w-full text-sm dark:bg-neutral-900 dark:text-neutral-100"
          />
        </div>
        {/* Reset & Export Buttons */}
        <div className="flex gap-2 mt-6">
          <button
            onClick={handleResetFilters}
            className="px-3 py-1 rounded border border-neutral-300 bg-white dark:bg-neutral-900 text-neutral-700 dark:text-neutral-200 text-sm hover:bg-neutral-100 dark:hover:bg-neutral-800"
          >
            Reset Filters
          </button>
          <button
            onClick={handleExportCSV}
            className="px-3 py-1 rounded  bg-[#00457f] dark:bg-[#00457f] text-white dark:text-white text-sm hover:bg-blue-900 dark:hover:bg-blue-800"
          >
            Export CSV
          </button>
        </div>
      </div>

      {/* Table Section */}
      <div className="mb-6">
        {/* <h2 className="text-xl font-bold text-neutral-800 dark:text-neutral-100 mb-2">Vulnerability Report</h2> */}
        <div className="bg-white dark:bg-[#202020] rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-4 overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead>
              <tr className="bg-[#EDF2F7] dark:bg-neutral-800">
                {columns.map(col => (
                  <th
                    key={col.key}
                    className="px-4 py-2 text-left text-xs font-medium text-neutral-700 dark:text-neutral-300 uppercase tracking-wider"
                  >
                    {col.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="divide-y divide-neutral-200 dark:divide-neutral-800">
              {paginatedResults.map((result, idx) => {
                const cvss = parseFloat(result.severity || '0');
                const computedThreat = getThreatLevelFromCvss(cvss);
                return (
                  <React.Fragment key={result._id || idx + (page - 1) * rowsPerPage}>
                    <tr className="hover:bg-neutral-50 dark:hover:bg-neutral-800/50 transition-colors">
                      {/* Host */}
                      <td className="px-4 py-2 text-sm">{result.host?.__text || result.host || '-'}</td>
                      {/* Port */}
                      <td className="px-4 py-2 text-sm">{result.port || '-'}</td>
                      {/* CVSS Score */}
                      <td className={`px-4 py-2 text-sm`}>
                        <span className={`px-2 py-1 rounded ${getCvssColor(Number(result.severity))}`}>{result.severity || '-'}</span>
                      </td>
                      {/* Threat Level (computed) */}
                      <td className={`px-4 py-2 text-sm ${getThreatColor(computedThreat)}`}>{computedThreat}</td>
                      {/* Affected Version */}
                      <td className="px-4 py-2 text-sm">{extractAffectedVersion(result)}</td>
                      {/* Solution */}
                      <td className="px-4 py-2 text-sm">{extractSolution(result)}</td>
                      {/* CVEs */}
                      <td className="px-4 py-2 text-sm">
                        {(() => {
                          const cves = extractCVEs(result) as string[];
                          if (Array.isArray(cves) && cves.length > 0) {
                            return (cves as string[]).map((cve, i) => <CveBadge key={cve + i} cve={cve} />);
                          }
                          return <span className="text-gray-400">-</span>;
                        })()}
                      </td>
                      {/* Details */}
                      <td className="px-4 py-2 text-sm">
                        <button
                          className="text-primary-600 underline font-semibold"
                          onClick={() => setExpanded(expanded === idx ? null : idx)}
                        >
                          View
                        </button>
                      </td>
                    </tr>
                    {renderDetails(result, idx + (page - 1) * rowsPerPage)}
                  </React.Fragment>
                );
              })}
              {paginatedResults.length === 0 && (
                <tr>
                  <td colSpan={columns.length} className="text-center py-8 text-neutral-500">
                    No vulnerabilities found.
                  </td>
                </tr>
              )}
            </tbody>
          </table>
          {/* Pagination Controls */}
          <div className="flex justify-between items-center mt-4">
            <div className="text-xs text-neutral-500">
              Showing {(filteredResults.length === 0 ? 0 : (page - 1) * rowsPerPage + 1)}-
              {Math.min(page * rowsPerPage, filteredResults.length)} of {filteredResults.length}
            </div>
            <div className="flex gap-2 items-center">
              <button
                className="px-3 py-1 rounded border text-sm disabled:opacity-50"
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Prev
              </button>
              <span className="px-2 text-sm">
                Page {page} of {totalPages || 1}
              </span>
              <button
                className="px-3 py-1 rounded border text-sm disabled:opacity-50"
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages || totalPages === 0}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Optional: Navigation or actions */}
      {/* <div className="flex justify-end">
        <button
          onClick={() => navigate('/assets-findings')}
          className="bg-primary-600 hover:bg-primary-700 text-white font-semibold px-6 py-2 rounded shadow transition"
        >
          View Detailed Findings
        </button>
      </div> */}
    </div>
  );
};

export default AssetsInventory;