import React, { create<PERSON>ontext, useContext, useState, useEffect, ReactNode, useRef, useCallback, useMemo } from 'react';
import scanApi from '../services/apiService';
import { ApiScanData } from '../types/types';


// Define types for our context data
interface DashboardStats {
  vulnerabilities: {
    total: number;
    highSeverity: number;
    mediumSeverity: number;
    lowSeverity: number;
  };
  openPorts: number;
  exposedEndpoints: number;
  exposedAssets: number;
  attackSurfaceIndex: number;
  threatScore: number;
  cyberHygieneScore: number;
  vendorRiskRating: number;
  complianceViolations: number;
  scanDate: string;
  subdomains: number;
  publicEndpoints: number;
}

interface Vulnerability {
  evidence: never[];
  solutions: never[];
  id: string;
  name: string;
  severity: string;
  endpoint: string;
  attackVector: string;
  description: string;
  remediation: string;
  complianceMapping: string[];
  alertRef?: string;
  confidence?: string;
  cweid?: string | number;
  desc?: string;
  pluginid?: string;
  reference?: string | string[];
  riskcode?: string;
  riskdesc?: string;
  instances?: { uri?: string; evidence?: string }[];
  solution?: string;
  sourceid?: string;
  wascid?: string;
}

interface Port {
  id: string;
  port: number;
  service: string;
  protocol: string;
  state: string;
  riskLevel: string;
  notes: string;
}

interface VulnerableEndpoint {
  id: string;
  path: string;
  method: string;
  vulnerabilityType: string;
  severity: string;
}

interface ScanFile {
  id: string;
  name: string;
  type: 'Defendly ' | 'Advanced';
  date: string;
  summary: {
    vulnerabilities: number;
    highSeverity: number;
    openPorts: number;
    endpoints: number;
  };
}

interface DashboardContextType {
  stats: DashboardStats;
  vulnerabilities: Vulnerability[];
  ports: Port[];
  endpoints: VulnerableEndpoint[];
  scanFiles: ScanFile[];
  loadingData: boolean;
  error: Error | null;
  refreshDashboard: () => Promise<void>;
  lastScanTime: number | null;
  filterVulnerabilities: (severity: string | null) => Vulnerability[];
  activeScanIds: string[];
  startScanPolling: (scanId: string) => void;
  stopScanPolling: (scanId: string) => void;
  initializeDashboard: () => Promise<void>;
  rawApiData: ApiScanData | null; // Add raw API data for components that need it
}


// Default values
const defaultStats: DashboardStats = {
  vulnerabilities: {
    total: 0,
    highSeverity: 0,
    mediumSeverity: 0,
    lowSeverity: 0,
  },
  openPorts: 0,
  exposedEndpoints: 0,
  exposedAssets: 0,
  attackSurfaceIndex: 0,
  threatScore: 0,
  cyberHygieneScore: 0,
  vendorRiskRating: 0,
  complianceViolations: 0,
  scanDate: '',
  subdomains: 0,
  publicEndpoints: 0 // Added missing property
};

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export const DashboardProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [stats, setStats] = useState<DashboardStats>(defaultStats);
  const [vulnerabilities, setVulnerabilities] = useState<Vulnerability[]>([]);
  const [ports, setPorts] = useState<Port[]>([]);
  const [endpoints, setEndpoints] = useState<VulnerableEndpoint[]>([]);
  const [scanFiles, setScanFiles] = useState<ScanFile[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [lastScanTime, setLastScanTime] = useState<number | null>(null);
  const [activeScanIds, setActiveScanIds] = useState<string[]>([]);
  const [rawApiData, setRawApiData] = useState<ApiScanData | null>(null);
  const activePollingRef = useRef<{[scanId: string]: () => void}>({});
  const isInitialFetchDone = useRef(false);

  // Load active scan IDs from localStorage on mount
  useEffect(() => {
    const storedActiveScanIds = localStorage.getItem('activeScanIds');
    if (storedActiveScanIds) {
      try {
        const scanIds = JSON.parse(storedActiveScanIds);
        if (Array.isArray(scanIds) && scanIds.length > 0) {
          setActiveScanIds(scanIds);

          // Resume polling for each active scan
          scanIds.forEach(scanId => {
            resumeScanPolling(scanId);
          });
        }
      } catch (error) {
        console.error("Failed to parse active scan IDs from localStorage:", error);
      }
    }
    

    // Removed automatic fetch on mount - we'll do this explicitly when authenticated
  }, []);

  const fetchDashboardData = async () => {
    // Set loading state immediately to indicate data is being fetched
    setLoadingData(true);
    setError(null);
    
    try {
      const scanData = await scanApi.getLatestScan();

      if (!scanData) {
        throw new Error('No scan data returned from API');
      }

      // Store raw API data for components that need it
      setRawApiData(scanData);

      // Process vulnerabilities with safer checks
      const processedVulnerabilities = Array.isArray(scanData.alerts) 
        ? scanData.alerts.map((alert: any, index: number) => {
            const severity = 
              alert.riskcode === "3" ? "high" : 
              alert.riskcode === "2" ? "medium" : 
              alert.riskcode === "1" ? "low" : "info";
            
            // Ensure instances exists and has items before accessing
            const endpoint = alert.instances && 
                           Array.isArray(alert.instances) && 
                           alert.instances.length > 0 && 
                           alert.instances[0].uri 
                             ? alert.instances[0].uri 
                             : "";
                             
            // Always ensure complianceMapping is an array, even if no cweid
            const complianceMapping = alert.cweid 
              ? [`CWE-${alert.cweid}`] 
              : [];
              
            return {
              id: alert.alertRef || index.toString(),
              name: alert.alert || 'Unknown Vulnerability',
              severity: severity,
              endpoint: endpoint,
              attackVector: alert.name || 'Unknown',
              description: alert.desc ? alert.desc.replace(/<\/p>/g, '') : 'No description available',
              remediation: alert.solution ? alert.solution.replace(/<\/p>/g, '') : 'No remediation available',
              complianceMapping: complianceMapping,
              // Map all new fields from the API response
              alertRef: alert.alertRef,
              confidence: alert.confidence,
              cweid: alert.cweid,
              desc: alert.desc,
              pluginid: alert.pluginid,
              reference: alert.reference,
              riskcode: alert.riskcode,
              riskdesc: alert.riskdesc,
              instances: alert.instances,
              solution: alert.solution,
              sourceid: alert.sourceid,
              wascid: alert.wascid,
            };
          }) 
        : [];
      
      setVulnerabilities(processedVulnerabilities);
      
      // Process open ports with proper service mapping
      const openPorts = scanData.attack_surface_index?.metrics?.open_ports;

      // Service mapping for common ports
      const getServiceInfo = (port: number) => {
        const serviceMap: Record<number, { service: string; riskLevel: 'high' | 'medium' | 'low'; protocol: string }> = {
          21: { service: 'FTP', riskLevel: 'high', protocol: 'tcp' },
          22: { service: 'SSH', riskLevel: 'medium', protocol: 'tcp' },
          23: { service: 'Telnet', riskLevel: 'high', protocol: 'tcp' },
          25: { service: 'SMTP', riskLevel: 'medium', protocol: 'tcp' },
          53: { service: 'DNS', riskLevel: 'low', protocol: 'tcp/udp' },
          80: { service: 'HTTP', riskLevel: 'medium', protocol: 'tcp' },
          110: { service: 'POP3', riskLevel: 'medium', protocol: 'tcp' },
          143: { service: 'IMAP', riskLevel: 'medium', protocol: 'tcp' },
          443: { service: 'HTTPS', riskLevel: 'low', protocol: 'tcp' },
          993: { service: 'IMAPS', riskLevel: 'low', protocol: 'tcp' },
          995: { service: 'POP3S', riskLevel: 'low', protocol: 'tcp' },
          1433: { service: 'MSSQL', riskLevel: 'high', protocol: 'tcp' },
          3306: { service: 'MySQL', riskLevel: 'high', protocol: 'tcp' },
          3389: { service: 'RDP', riskLevel: 'high', protocol: 'tcp' },
          5432: { service: 'PostgreSQL', riskLevel: 'high', protocol: 'tcp' },
          5900: { service: 'VNC', riskLevel: 'high', protocol: 'tcp' },
          6379: { service: 'Redis', riskLevel: 'high', protocol: 'tcp' },
          8080: { service: 'HTTP-Proxy', riskLevel: 'medium', protocol: 'tcp' },
          8443: { service: 'HTTPS-Alt', riskLevel: 'medium', protocol: 'tcp' },
          9200: { service: 'Elasticsearch', riskLevel: 'high', protocol: 'tcp' },
          27017: { service: 'MongoDB', riskLevel: 'high', protocol: 'tcp' }
        };

        return serviceMap[port] || {
          service: `Service-${port}`,
          riskLevel: 'low' as const,
          protocol: 'tcp'
        };
      };

      const processedPorts = Array.isArray(openPorts)
        ? openPorts.map((port: number, index: number) => {
            const serviceInfo = getServiceInfo(port);
            return {
              id: index.toString(),
              port: port,
              service: serviceInfo.service,
              protocol: serviceInfo.protocol,
              state: "open",
              riskLevel: serviceInfo.riskLevel,
              notes: `${serviceInfo.service} service running on port ${port}`
            };
          })
        : [];

      setPorts(processedPorts);
      
      // Process endpoints by mapping vulnerabilities to their endpoints
      const processedEndpoints: VulnerableEndpoint[] = [];

      // Create endpoints from vulnerability data
      processedVulnerabilities.forEach((vuln: Vulnerability, index: number) => {
        if (vuln.endpoint) {
          // Extract method from vulnerability data or default to GET
          const method = vuln.attackVector?.includes('POST') ? 'POST' :
                        vuln.attackVector?.includes('PUT') ? 'PUT' :
                        vuln.attackVector?.includes('DELETE') ? 'DELETE' : 'GET';

          processedEndpoints.push({
            id: `endpoint-${index}`,
            path: vuln.endpoint,
            method: method,
            vulnerabilityType: vuln.name,
            severity: vuln.severity
          });
        }
      });

      setEndpoints(processedEndpoints);
      
      // Update stats with safer access and explicit logging
      const highSeverity: number = processedVulnerabilities.filter((v: Vulnerability) => v.severity === 'high').length;
      const mediumSeverity: number = processedVulnerabilities.filter((v: Vulnerability) => v.severity === 'medium').length;
      const lowSeverity: number = processedVulnerabilities.filter((v: Vulnerability) => v.severity === 'low').length;
      
      const attackSurfaceIndex = scanData.attack_surface_index?.score;

      setStats({
        vulnerabilities: {
          total: processedVulnerabilities.length,
          highSeverity,
          mediumSeverity,
          lowSeverity,
        },
        openPorts: scanData.attack_surface_index?.metrics?.open_ports_count || 0,
        exposedEndpoints: scanData.endpoints?.total_count || 0,
        exposedAssets: scanData.attack_surface_index?.metrics?.exposed_services_count || 0,
        attackSurfaceIndex: typeof attackSurfaceIndex === 'number' ? attackSurfaceIndex : 0,
        threatScore: scanData.threat_intelligence?.threat_intelligence_score?.score || 0,
        cyberHygieneScore: scanData.cyber_hygiene_score?.score || 0,
        vendorRiskRating: scanData.vendor_risk_rating?.numeric_rating || 0,
        complianceViolations: scanData.compliance_readiness?.total_owasp_issues || 0,
        scanDate: scanData.scan_date || new Date().toISOString(),
        subdomains: scanData.attack_surface_index?.metrics?.subdomains_count || 0,
        publicEndpoints: scanData.endpoints?.endpoints?.length || 0
      });

      // Process scan files
      const processedScanFiles: ScanFile[] = Array.isArray(scanData.scans)
        ? scanData.scans.map((scan: any) => {
            // Try all possible fields for the scan name
            const scanName =
              scan.url ||
              scan.target ||
              scan.target_url ||
              scan.domain ||
              scan.host ||
              scan.ip ||
              scan.id?.toString() ||
              'Unnamed Scan';

            // Count vulnerabilities for this scan
            const scanVulnCount = Array.isArray(scanData.alerts)
              ? scanData.alerts.filter((alert: any) => {
                  // Adjust this field if your alert object uses a different property for scan id
                  return alert.scan_id === scan.id;
                }).length
              : 0;

            return {
              id: scan.id?.toString() || 'unknown',
              name: scanName,
              type: scan.type || 'Defendly ',
              date: scan.scan_date || new Date().toISOString(),
              summary: {
                vulnerabilities: scanVulnCount,
                highSeverity: scan.open_ports_count || 0,
              }
            };
          })
        : [
            // fallback if scanData.scans is not an array
            {
              id: scanData.id?.toString() || 'unknown',
              name:
                scanData.url ||
                scanData.target ||
                scanData.target_url ||
                scanData.domain ||
                scanData.host ||
                scanData.ip ||
                scanData.id?.toString() ||
                'Unnamed Scan',
              type: scanData.type || 'Defendly ',
              date: scanData.scan_date || new Date().toISOString(),
              summary: {
                vulnerabilities: Array.isArray(scanData.alerts) ? scanData.alerts.length : 0,
                highSeverity: openPorts,
              }
            }
          ];

      setScanFiles(processedScanFiles);

      setLastScanTime(Date.now());
      isInitialFetchDone.current = true;
      
    } catch (err) {
      console.error('Error fetching dashboard data:', err);
      setError(err instanceof Error ? err : new Error('An unknown error occurred'));
    } finally {
      setLoadingData(false);
    }
  };

  // Helper function to resume polling for a scan
  const resumeScanPolling = async (scanId: string) => {
    console.log(`Resuming polling for scan ${scanId}`);
    
    // Start polling for this scan
    const stopPolling = await scanApi.pollScanStatus(scanId, (status) => {
      console.log(`Scan ${scanId} status update: ${status}`);
      
      // If scan is completed, remove it from active scans
      if (status === 'completed' || status === 'failed') {
        stopScanPolling(scanId);
        fetchDashboardData(); // Refresh dashboard data when scan completes
      }
    });
    
    // Store the stop function for cleanup
    activePollingRef.current[scanId] = stopPolling;
  };

  // Initialize dashboard data - only called when user is authenticated
  const initializeDashboard = async (): Promise<void> => {
    // Remove the loading check as it might block initialization
    if (!isInitialFetchDone.current) {
      try {
        await fetchDashboardData();
      } catch (error) {
        console.error('Error initializing dashboard');
      }
    }
  };

  // Update refreshDashboard function to return a promise
  const refreshDashboard = async (): Promise<void> => {
    await fetchDashboardData();
  };

  // Memoized filter function for better performance
  const filterVulnerabilities = useCallback((severity: string | null): Vulnerability[] => {
    if (!severity || severity === 'all') {
      return vulnerabilities;
    }
    return vulnerabilities.filter(v => v.severity === severity);
  }, [vulnerabilities]);

  // Memoized function to start polling for a scan
  const startScanPolling = useCallback((scanId: string) => {
    setActiveScanIds(prev => {
      // Add scanId if it doesn't already exist
      if (!prev.includes(scanId)) {
        const newActiveScanIds = [...prev, scanId];
        localStorage.setItem('activeScanIds', JSON.stringify(newActiveScanIds));
        resumeScanPolling(scanId);
        return newActiveScanIds;
      }
      return prev;
    });
  }, []);

  // Memoized function to stop polling for a scan
  const stopScanPolling = useCallback((scanId: string) => {
    // Call the stop function if it exists
    if (activePollingRef.current[scanId]) {
      activePollingRef.current[scanId]();
      delete activePollingRef.current[scanId];
    }

    setActiveScanIds(prev => {
      const newActiveScanIds = prev.filter(id => id !== scanId);
      localStorage.setItem('activeScanIds', JSON.stringify(newActiveScanIds));
      return newActiveScanIds;
    });
  }, []);

  // Clean up all polling on unmount
  useEffect(() => {
    return () => {
      Object.values(activePollingRef.current).forEach(stopPolling => {
        if (typeof stopPolling === 'function') {
          stopPolling();
        }
      });
    };
  }, []);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    stats,
    vulnerabilities,
    ports,
    endpoints,
    scanFiles,
    loadingData,
    error,
    refreshDashboard,
    lastScanTime,
    filterVulnerabilities,
    activeScanIds,
    startScanPolling,
    stopScanPolling,
    initializeDashboard,
    rawApiData
  }), [
    stats,
    vulnerabilities,
    ports,
    endpoints,
    scanFiles,
    loadingData,
    error,
    refreshDashboard,
    lastScanTime,
    filterVulnerabilities,
    activeScanIds,
    startScanPolling,
    stopScanPolling,
    initializeDashboard,
    rawApiData
  ]);

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
};

export const useDashboard = () => {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
};