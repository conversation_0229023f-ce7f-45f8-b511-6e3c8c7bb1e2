
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Brain, ArrowLeft } from 'lucide-react';
import { useDashboard } from '../context/DashboardContext';
import scanApi from '../services/apiService';

interface ScanFormData {
  organization: string;
  projectName: string;
  targetAsset: string;
  scanTarget: string;
  label?: string;
  schedule: 'now' | 'later';
  scheduledDate?: string;
  scheduledTime?: string;
  disclaimer?: boolean;
}

const ScanReview: React.FC = () => {
  const navigate = useNavigate();
  const { startScanPolling } = useDashboard();
  const [formData, setFormData] = useState<ScanFormData | null>(null);
  const [confirmed, setConfirmed] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const storedData = localStorage.getItem('scanFormData');
    if (storedData) {
      setFormData(JSON.parse(storedData));
    } else {
      navigate('/assessment');
    }
  }, [navigate]);

  const handleConfirm = async () => {
    if (confirmed && formData) {
      try {
        setLoading(true);
        
        // Call API to initiate scan
        const response = await scanApi.initiateScan(formData.scanTarget);
        const scanId = response.scan_id;
        
        // Create a new scan entry for localStorage
        const newScan = {
          id: parseInt(scanId) || Date.now(),
          asset: formData.scanTarget,
          organization: formData.organization,
          project: formData.projectName,
          type: 'AI Scan',
          startDate: new Date().toISOString().split('T')[0],
          startTime: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
          endDate: '—',
          endTime: '—',
          status: 'pending',
          description: `Security assessment for ${formData.targetAsset}: ${formData.scanTarget}`,
          details: {
            vulnerabilities: 0,
            endpoints: 0,
            openPorts: 0,
            severity: { high: 0, medium: 0, low: 0 }
          }
        };
        
        // Add to recent scans in localStorage
        const existingScans = JSON.parse(localStorage.getItem('recentScans') || '[]');
        const updatedScans = [newScan, ...existingScans];
        localStorage.setItem('recentScans', JSON.stringify(updatedScans));
        
        // Start polling for scan status
        startScanPolling(scanId.toString());
        
        // Clean up and navigate back
        localStorage.removeItem('scanFormData');
        navigate('/assessment');
        
      } catch (error) {
        console.error('Error initiating scan:', error);
        // Handle error state
      } finally {
        setLoading(false);
      }
    }
  };

  if (!formData) {
    return null;
  }

  const renderInfoCard = (title: string, value: string) => (
    <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 p-4">
      <h3 className="text-sm font-medium text-neutral-600 dark:text-neutral-300 mb-1">{title}</h3>
      <p className="text-base text-neutral-800 dark:text-neutral-100">{value}</p>
    </div>
  );

  return (
    <div className="container mx-auto px-4 py-6">
      <button
        onClick={() => navigate('/assessment')}
        className="flex items-center text-neutral-400 hover:text-neutral-600 dark:text-neutral-300 dark:hover:text-neutral-100 transition-colors mb-4"
      >
        <ArrowLeft className="w-4 h-4 mr-1" />
        <span>Back</span>
      </button>

      <div className="mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Review Scan Details</h1>
        <p className="text-neutral-600 dark:text-neutral-300">Please review and confirm the scan configuration before proceeding</p>
      </div>

      <div className="max-w-3xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {renderInfoCard("Organization", formData.organization)}
          {renderInfoCard("Project Name", formData.projectName)}
          {renderInfoCard("Target Asset Type", formData.targetAsset)}
          {renderInfoCard("Scan Target", formData.scanTarget)}
          {formData.label && renderInfoCard("Label", formData.label)}
          {renderInfoCard("Schedule", formData.schedule === 'now' ? 'Immediate Execution' : 'Scheduled')}
          {formData.schedule === 'later' && formData.scheduledDate && (
            renderInfoCard("Scheduled Date", formData.scheduledDate)
          )}
          {formData.schedule === 'later' && formData.scheduledTime && (
            renderInfoCard("Scheduled Time", formData.scheduledTime)
          )}
        </div>

        <div className="bg-neutral-50 dark:bg-neutral-800 rounded-lg p-6 mb-6">
          <div className="flex items-center mb-4">
            <input
              type="checkbox"
              id="confirmation"
              checked={confirmed}
              onChange={(e) => setConfirmed(e.target.checked)}
              className="w-4 h-4 text-primary-500 border-neutral-300 rounded focus:ring-primary-500"
            />
            <label htmlFor="confirmation" className="ml-2 text-sm text-neutral-700 dark:text-neutral-200">
              I have reviewed all scan details and confirm they are correct and authorized for execution.
            </label>
          </div>

          <button
            onClick={handleConfirm}
            disabled={!confirmed || loading}
            className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-primary-500 text-white rounded-md font-medium hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {loading ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                PROCESSING...
              </>
            ) : (
              <>
                <Brain className="w-5 h-5" />
                RUN AI SCAN
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ScanReview;