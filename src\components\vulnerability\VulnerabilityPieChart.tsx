import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, LabelList } from 'recharts';

interface VulnerabilityData {
  name: string;
  value: number;
  color: string;
  percentage: number;
}

interface Props {
  data: VulnerabilityData[];
  onSegmentClick: (severity: string) => void;
  selectedSeverity: string | null;
}

const VulnerabilityPieChart: React.FC<Props> = ({ data, onSegmentClick, selectedSeverity }) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  // Custom label component that won't disappear on hover
  const CustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
    const RADIAN = Math.PI / 180;
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);

    if (percent > 0) {
      return (
        <text
          x={x}
          y={y}
          fill="#FFFFFF"
          textAnchor="middle"
          dominantBaseline="central"
          fontSize="16"
          fontWeight="700"
          fontFamily="Inter, sans-serif"
          style={{
            pointerEvents: 'none',
            userSelect: 'none',
            zIndex: 1000
          }}
        >
          {`${(percent * 100).toFixed(0)}%`}
        </text>
      );
    }
    return null;
  };

  // Handle cell click with better event handling
  const handleCellClick = useCallback((entry: any, index: number) => {
    console.log('Pie chart segment clicked:', entry.name);
    onSegmentClick(entry.name.toLowerCase());
  }, [onSegmentClick]);

  // Simple mouse event handlers
  const handleMouseEnter = (index: number) => {
    setHoveredIndex(index);
  };

  const handleMouseLeave = () => {
    setHoveredIndex(null);
  };

  return (
    <div className="flex items-center justify-center h-full">
      <div
        style={{
          width: '312.88px',
          height: '312.88px',
          borderRadius: '10px',
          overflow: 'hidden',
          outline: 'none', // Remove any outline
          border: 'none'   // Remove any border
        }}
        className="focus:outline-none" // Remove focus outline
      >
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={80}
              outerRadius={140}
              paddingAngle={2}
              dataKey="value"
              label={CustomLabel}
              labelLine={false}
              isAnimationActive={false} // <-- Add this line to disable animation
            >
              {data.map((entry, index) => {
                const isSelected = selectedSeverity === entry.name.toLowerCase();
                const isHovered = hoveredIndex === index;

                return (
                  <Cell
                    key={`cell-${index}`}
                    fill={entry.color}
                    stroke={isSelected ? '#ffffff' : 'transparent'}
                    strokeWidth={isSelected ? 4 : 0}
                    style={{
                      cursor: 'pointer',
                      outline: 'none',
                      border: 'none',
                      opacity: isHovered ? 0.85 : 1,
                      filter: isHovered ? 'brightness(1.05)' : 'none'
                    }}
                    className="focus:outline-none transition-all duration-200"
                    onClick={() => handleCellClick(entry, index)}
                    onMouseEnter={() => handleMouseEnter(index)}
                    onMouseLeave={() => handleMouseLeave()}
                  />
                );
              })}
            </Pie>
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default VulnerabilityPieChart;
