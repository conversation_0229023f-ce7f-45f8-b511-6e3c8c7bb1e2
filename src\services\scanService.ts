import { parseZapData } from '../utils/parseZapData';
import { parseNmapData } from '../utils/parseNmapData';
import { getScanData, setScanData, ScanData } from '../utils/scanDataLoader';

export const performScan = async (request: ScanRequest): Promise<ScanData> => {
  try {
    // Use the new base URL for the scan API
    const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/scan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      throw new Error('Failed to perform scan');
    }

    const data = await response.json();
    const parsedData = {
      zapData: parseZapData(data.zap),
      nmapData: parseNmapData(data.nmap),
    };

    setScanData(parsedData);
    return parsedData;
  } catch (error) {
    console.error('Error performing scan:', error);
    throw error;
  }
};