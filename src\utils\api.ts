/**
 * Centralized API utility for making requests to the backend
 */
import TokenManager from './tokenManager';

// Get the base URL from environment variables
const BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://aipt-be-server.onrender.com';

// Helper function for making API requests
export const apiRequest = async (
  endpoint: string, 
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
  data?: any,
  token?: string
) => {
  const url = `${BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
  
  const headers: HeadersInit = {
    'Content-Type': 'application/json',
  };

  // Add authorization token if provided or get from TokenManager
  const authToken = token || TokenManager.getToken();
  if (authToken && !TokenManager.isTokenExpired()) {
    headers['Authorization'] = `Bearer ${authToken}`;
  }

  const options: RequestInit = {
    method,
    headers,
    credentials: 'include', // Include cookies in requests if needed
  };

  // Add request body for POST/PUT requests
  if (data && (method === 'POST' || method === 'PUT')) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);

    // Handle non-2xx responses
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.message || `API Error: ${response.status} ${response.statusText}`;
      throw new Error(errorMessage);
    }

    // Parse JSON response, or return empty object if no content
    const result = await response.json().catch(() => ({}));
    return result;
  } catch (error) {
    // Log error without exposing sensitive information
    console.error('API Request Error');

    // Re-throw with sanitized error message
    if (error instanceof Error) {
      throw new Error(error.message);
    }
    throw new Error('An unexpected error occurred');
  }
};

// Export specialized methods for convenience
export const apiGet = (endpoint: string, token?: string) => apiRequest(endpoint, 'GET', undefined, token);
export const apiPost = (endpoint: string, data: any, token?: string) => apiRequest(endpoint, 'POST', data, token);
export const apiPut = (endpoint: string, data: any, token?: string) => apiRequest(endpoint, 'PUT', data, token);
export const apiDelete = (endpoint: string, token?: string) => apiRequest(endpoint, 'DELETE', undefined, token);
