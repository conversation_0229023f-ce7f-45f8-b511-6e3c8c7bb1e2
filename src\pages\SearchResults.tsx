
import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useDashboard } from '../context/DashboardContext';
import { Search, AlertTriangle, ArrowLeft } from 'lucide-react';
import VulnerabilitiesTable from '../components/dashboard/VulnerabilitiesTable';
import OpenPortsTable from '../components/dashboard/OpenPortsTable';
import VulnerableEndpoints from '../components/dashboard/VulnerableEndpoints';

const SearchResults: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { processNLPQuery, filterVulnerabilities } = useDashboard();
  
  // Get the query param from URL
  const queryParams = new URLSearchParams(location.search);
  const query = queryParams.get('q') || '';
  
  // Process the NLP query
  const [results, setResults] = React.useState<any>({ type: '', data: [], message: '' });
  const [loading, setLoading] = React.useState(true);
  
  React.useEffect(() => {
    const fetchResults = async () => {
      setLoading(true);
      try {
        const queryResults = await processNLPQuery(query);
        setResults(queryResults);
      } catch (error) {
        console.error('Error processing query:', error);
      } finally {
        setLoading(false);
      }
    };
    
    if (query) {
      fetchResults();
    }
  }, [query, processNLPQuery]);
  
  const handleFilterChange = (severity: string | null) => {
    // Implementation depends on how you want to handle filtering in search results
  };
  
  const renderResultsContent = () => {
    switch (results.type) {
      case 'vulnerabilities':
        return (
          <div className="dashboard-card bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-4">
            <VulnerabilitiesTable 
              vulnerabilities={results.data} 
              onFilterChange={handleFilterChange}
            />
          </div>
        );
        
      case 'endpoints':
        return (
          <div className="dashboard-card bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-4">
            <VulnerableEndpoints endpoints={results.data} />
          </div>
        );
        
      case 'ports':
        return (
          <div className="dashboard-card bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-4">
            <OpenPortsTable ports={results.data} />
          </div>
        );
        
      case 'reports':
        return (
          <div className="dashboard-card p-5 bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800">
            <h3 className="text-lg font-semibold mb-4 dark:text-neutral-100">Report Results</h3>
            <p className="dark:text-neutral-200">Found {results.data.length} reports matching your query.</p>
            <button 
              className="mt-4 bg-primary-500 hover:bg-primary-600 text-white px-4 py-2 rounded-md"
              onClick={() => navigate('/reports')}
            >
              View All Reports
            </button>
          </div>
        );
        
      default:
        return (
          <div className="dashboard-card p-5 text-center bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800">
            <AlertTriangle className="w-12 h-12 text-warning-500 mx-auto mb-3" />
            <h3 className="text-lg font-semibold mb-2 dark:text-neutral-100">No Results Found</h3>
            <p className="text-neutral-600 dark:text-neutral-300">
              We couldn't find any results matching your query. Try asking about vulnerabilities, endpoints, ports, or reports.
            </p>
          </div>
        );
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-6">
      <button 
        className="flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mb-4"
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="w-4 h-4 mr-1" />
        Back
      </button>
      
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Search Results</h1>
        <div className="flex items-center text-neutral-600 dark:text-neutral-300">
          <Search className="w-4 h-4 mr-2" />
          <p>Results for: <span className="font-medium">{query}</span></p>
        </div>
      </div>
      
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-12 h-12 border-4 border-neutral-200 dark:border-neutral-800 border-t-primary-500 rounded-full animate-spin"></div>
        </div>
      ) : (
        <>
          <div className="mb-4 bg-primary-50 dark:bg-primary-900 p-4 rounded-md">
            <div className="font-medium text-primary-700 dark:text-primary-300">AI Interpretation</div>
            <p className="text-primary-600 dark:text-primary-200 text-sm">{results.message}</p>
          </div>
          
          {renderResultsContent()}
        </>
      )}
    </div>
  );
};

export default SearchResults;