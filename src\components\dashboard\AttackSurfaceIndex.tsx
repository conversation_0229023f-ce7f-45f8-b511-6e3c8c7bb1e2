import React from "react";
import { useDashboard } from "../../context/DashboardContext";

const AttackSurfaceIndex: React.FC = () => {
  const { stats, rawApiData } = useDashboard();

  // Get data from API response with fallbacks
  const attackSurfaceData = rawApiData?.attack_surface_index;
  const metrics = attackSurfaceData?.metrics;

  const exposedAssets = metrics?.exposed_services_count ?? stats?.exposedAssets ?? 4;
  const openPorts = metrics?.open_ports_count ?? stats?.openPorts ?? 4;
  const publicIPs = metrics?.public_ips_count ?? stats?.publicEndpoints ?? 2;
  const subdomains = metrics?.subdomains_count ?? stats?.subdomains ?? 1;

  // Mock percentage changes
  const getPercentageChange = (metric: string) => {
    switch (metric) {
      case "exposedAssets":
        return { value: 0.5, isPositive: true };
      case "publicIPs":
        return { value: 0.5, isPositive: false };
      case "openPorts":
        return { value: 0.5, isPositive: true };
      case "subdomains":
        return { value: 0.5, isPositive: false };
      default:
        return { value: 0, isPositive: true };
    }
  };

  // Progress bar component
  const ProgressBar = ({ value, maxValue, color }: { value: number; maxValue: number; color: string }) => {
    const percentage = Math.min((value / maxValue) * 100, 100);

    return (
      <div className="flex items-center gap-3 mb-2">
        {/* Progress Bar - full width */}
        <div className="flex-1 h-3 bg-neutral-300 dark:bg-neutral-700 rounded-full min-w-0">
          <div
            className="h-3 rounded-full transition-all duration-500"
            style={{
              width: `${percentage}%`,
              backgroundColor: color
            }}
          />
        </div>

        {/* Number */}
        <span className="text-4xl font-bold flex-shrink-0" style={{ color }}>
          {value}
        </span>
      </div>
    );
  };

  // Metric card component
  const MetricCard = ({
    title,
    value,
    color,
    changeData
  }: {
    title: string;
    value: number;
    color: string;
    changeData: { value: number; isPositive: boolean }
  }) => {
    return (
      <div className=" border border-neutral-300 dark:border-[#666666] rounded-lg p-3 flex flex-col overflow-hidden" style={{ height: '110px' }}>
        {/* Title */}
        <h4 className="text-neutral-900 dark:text-white text-sm font-semibold mb-2">{title}</h4>

        {/* Progress Bar with Number */}
        <div className="flex-1 flex flex-col justify-center">
          <ProgressBar value={value} maxValue={10} color={color} />

          {/* Percentage change below number */}
          <div className="flex items-center gap-1 justify-end">
            <span className={`text-xs ${changeData.isPositive ? 'text-green-400' : 'text-red-400'}`}>
              {changeData.isPositive ? '▲' : '▼'}
            </span>
            <span className={`text-xs font-bold ${changeData.isPositive ? 'text-green-400' : 'text-red-400'}`}>
              {changeData.isPositive ? '+' : ''}{changeData.value}%
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-4 flex flex-col w-full">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-1 mr-3">
          <h3 className="text-lg font-semibold text-neutral-900 dark:text-white">
            Attack Surface Index
          </h3>
          <div className="tooltip-container relative group">
            <div className="w-5 h-5 rounded-full border border-neutral-400 dark:border-neutral-400 flex items-center justify-center cursor-help">
              <span className="text-xs text-neutral-400">i</span>
            </div>
            <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
              Measures your organization's exposure to potential attacks through exposed assets, open ports, public IPs, and discoverable subdomains.
            </div>
          </div>
        </div>
        {/* <button className="bg-transparent border border-neutral-400 dark:border-white rounded-md px-2 py-1 text-xs text-neutral-700 dark:text-neutral-300 hover:border-neutral-500 transition-colors">
          View Details
        </button> */}
      </div>

      {/* Cards Grid */}
      <div className="grid grid-cols-2 gap-4 flex-1">
        {/* Exposed Assets */}
        <MetricCard
          title="Exposed Assets"
          value={exposedAssets}
          color="#3B82F6"
          changeData={getPercentageChange("exposedAssets")}
        />

        {/* Public IP's */}
        <MetricCard
          title="Public IP's"
          value={publicIPs}
          color="#EF4444"
          changeData={getPercentageChange("publicIPs")}
        />

        {/* Open Ports */}
        <MetricCard
          title="Open Ports"
          value={openPorts}
          color="#F59E0B"
          changeData={getPercentageChange("openPorts")}
        />

        {/* Subdomains */}
        <MetricCard
          title="Subdomains"
          value={subdomains}
          color="#3B82F6"
          changeData={getPercentageChange("subdomains")}
        />
      </div>
    </div>
  );
};

export default AttackSurfaceIndex;