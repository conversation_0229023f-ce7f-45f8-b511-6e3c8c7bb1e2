import React, { useEffect, useRef } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { useDashboard } from '../../context/DashboardContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, loading } = useAuth();
  const { initializeDashboard } = useDashboard();
  const location = useLocation();
  const initializationAttempted = useRef(false);

  // When going to a protected route and user is authenticated
  useEffect(() => {
    // Simplified condition - just check if authenticated and not loading
    if (isAuthenticated && !loading) {
      console.log('ProtectedRoute: User authenticated, initializing dashboard...');
      // Always try to initialize when authenticated - the context will handle duplicate calls
      initializeDashboard().catch(err => {
        console.error('Failed to initialize dashboard:', err);
      });
    } else {
      console.log('ProtectedRoute: Skipping dashboard initialization - auth status:', isAuthenticated, 'loading:', loading);
    }
  }, [isAuthenticated, loading, initializeDashboard]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="w-12 h-12 border-4 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;