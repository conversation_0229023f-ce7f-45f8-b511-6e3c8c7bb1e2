import React, { useState, useMemo } from "react";
import { Info, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useDashboard } from "../../context/DashboardContext";

const SecurityHeaders = React.memo(() => {
  const { rawApiData } = useDashboard();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'present' | 'missing'>('present');
  const [showAll, setShowAll] = useState(false);

  // Get security misconfigurations from API data
  const securityData = rawApiData?.security_misconfigurations;
  const missingHeaders = securityData?.missing_security_headers?.headers || [];

  // Define all possible security headers with their descriptions
  const allHeaders = [
    {
      name: "content-security-policy",
      description: "Controls resources the user agent is allowed to load for a given page.",
    },
    {
      name: "strict-transport-security",
      description: "Forces connections over HTTPS instead of HTTP.",
    },
    {
      name: "x-frame-options",
      description: "Indicates whether a browser should be allowed to render a page in a frame.",
    },
    {
      name: "x-content-type-options",
      description: "Prevents MIME type sniffing attacks.",
    },
    {
      name: "referrer-policy",
      description: "Controls how much referrer information should be included with requests.",
    },
    {
      name: "permissions-policy",
      description: "Controls which features and APIs can be used in the browser.",
    },
    {
      name: "cross-origin-embedder-policy",
      description: "Prevents a document from loading any cross-origin resources.",
    },
    {
      name: "cross-origin-opener-policy",
      description: "Isolates browsing context exclusively to same-origin documents.",
    },
    {
      name: "cross-origin-resource-policy",
      description: "Conveys a desire that the browser blocks no-cors cross-origin/cross-site requests.",
    },
  ];

  // Determine which headers are present/missing based on API data
  const headers = useMemo(() => {
    return allHeaders.map(header => ({
      ...header,
      present: !missingHeaders.some(missing =>
        missing.toLowerCase().includes(header.name.toLowerCase()) ||
        header.name.toLowerCase().includes(missing.toLowerCase())
      )
    }));
  }, [missingHeaders]);

  const presentHeaders = headers.filter(h => h.present);
  const missingHeadersList = headers.filter(h => !h.present);
  const allDisplayHeaders = activeTab === 'present' ? presentHeaders : missingHeadersList;
  const displayHeaders = showAll ? allDisplayHeaders : allDisplayHeaders.slice(0, 4);
  return (
    <div
      className="bg-white dark:bg-[#202020] rounded-[10px] shadow-sm border border-neutral-200 dark:border-[#232323] p-6 flex flex-col h-full"
      // style={{ width: "340px", minWidth: 0 }}
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-1 whitespace-nowrap mr-3">
          <h3 className="text-base font-semibold text-neutral-900 dark:text-white whitespace-nowrap">
            Security Headers
          </h3>
          <div className="tooltip-container relative group">
            <Info className="w-4 h-4 text-neutral-400 dark:text-neutral-400 ml-1 cursor-help" />
            <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
              HTTP security headers that protect against common web vulnerabilities like XSS, clickjacking, and MIME sniffing attacks. Missing headers indicate potential security gaps.
            </div>
          </div>
        </div>
        {/* <button className="bg-transparent border border-neutral-400 dark:border-neutral-600 rounded-md px-2 py-1 text-xs text-neutral-700 dark:text-neutral-300 hover:border-neutral-500 dark:hover:border-neutral-500 transition-colors whitespace-nowrap">
          View Details
        </button> */}
      </div>

      {/* Tabs */}
      <div className="flex mb-4">
        <button
          className={`flex-1 py-2 rounded-tl-lg font-semibold text-xs transition-colors ${
            activeTab === 'present'
              ? 'bg-[#00457f] text-white'
              : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-400'
          }`}
          onClick={() => setActiveTab('present')}
        >
          Present ({presentHeaders.length})
        </button>
        <button
          className={`flex-1 py-2 rounded-tr-lg font-semibold text-xs transition-colors ${
            activeTab === 'missing'
              ? 'bg-[#00457f] text-white'
              : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-400'
          }`}
          onClick={() => setActiveTab('missing')}
        >
          Missing ({missingHeadersList.length})
        </button>
      </div>

      {/* Headers List */}
      <div className="space-y-3 flex-1 overflow-y-auto">
        {displayHeaders.map((header) => (
          <div
            key={header.name}
            className={`border rounded-lg px-4 py-3 ${
              header.present
                ? "border-green-600"
                : "border-red-600"
            } `}
          >
            <div className="flex items-center justify-between">
              <span className="font-semibold text-neutral-900 dark:text-white text-sm">{header.name}</span>
              <span
                className={`ml-2 w-6 h-6 flex items-center justify-center rounded-full border text-xs ${
                  header.present
                    ? "border-green-600 text-green-500"
                    : "border-red-600 text-red-500"
                }`}
              >
                {header.present ? "✓" : "✗"}
              </span>
            </div>
            {header.description && (
              <div className="mt-2 text-xs text-neutral-700 dark:text-neutral-300">{header.description}</div>
            )}
          </div>
        ))}
      </div>

      {/* Show All Button */}
      {!showAll && allDisplayHeaders.length > 4 && (
        <div className="mt-4 flex justify-center">
          <button
            onClick={() => navigate("/security-headers")}
            className="flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm"
          >
            Show All
            <ArrowRight className="w-4 h-4 ml-1" />
          </button>
        </div>
      )}
    </div>
  );
});

export default SecurityHeaders;