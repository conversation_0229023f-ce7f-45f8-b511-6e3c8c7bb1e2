# Cybersecurity News API Setup

This document explains how to set up real-time cybersecurity news integration for the Defendly dashboard.

## 🌍 Supported News APIs

### 1. NewsAPI (Primary)
- **Website**: https://newsapi.org/
- **Free Tier**: 1,000 requests/day
- **Coverage**: Global news sources
- **Best For**: Development and small-scale production

### 2. GNews (Alternative)
- **Website**: https://gnews.io/
- **Free Tier**: 100 requests/day
- **Coverage**: Global news in 60+ languages
- **Best For**: Backup source

### 3. NewsData.io (Alternative)
- **Website**: https://newsdata.io/
- **Free Tier**: 200 requests/day
- **Coverage**: Global news sources
- **Best For**: Additional coverage

## 🔧 Setup Instructions

### Step 1: Get API Keys

1. **NewsAPI** (Recommended):
   - Visit https://newsapi.org/register
   - Sign up for a free account
   - Copy your API key

2. **GNews** (Optional):
   - Visit https://gnews.io/
   - Sign up for a free account
   - Copy your API key

### Step 2: Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Add your API keys to `.env`:
   ```env
   REACT_APP_NEWS_API_KEY=your_actual_newsapi_key_here
   REACT_APP_GNEWS_API_KEY=your_actual_gnews_key_here
   ```

3. Restart your development server:
   ```bash
   npm start
   ```

### Step 3: Verify Integration

The news component will automatically:
- Try NewsAPI first (if key is provided)
- Fallback to GNews (if NewsAPI fails)
- Use mock data (if all APIs fail)

## 📰 News Categories

The system automatically filters for cybersecurity-related news including:

- **Vulnerabilities**: Zero-day exploits, CVEs, security flaws
- **Attacks**: Ransomware, malware, data breaches
- **Patches**: Security updates, fixes, advisories
- **Threats**: Phishing, social engineering, APTs
- **Industry**: Security trends, compliance, regulations

## 🎯 Features

### Real-time Updates
- Fetches latest news every 30 minutes
- Automatic refresh in background
- Error handling with fallbacks

### Smart Categorization
- Icons based on news content
- Color-coded by threat type
- Time-based sorting

### Interactive Elements
- Click to open full article
- Hover effects and transitions
- External link indicators

## 🔒 Security Considerations

### API Key Protection
- Never commit API keys to version control
- Use environment variables only
- Rotate keys regularly

### Rate Limiting
- Built-in request throttling
- Automatic fallback to alternatives
- Graceful degradation to mock data

### Content Filtering
- Keyword-based relevance filtering
- Source reputation checking
- Malicious link protection

## 🚀 Production Deployment

### Environment Variables
Set these in your production environment:
```env
REACT_APP_NEWS_API_KEY=your_production_newsapi_key
REACT_APP_GNEWS_API_KEY=your_production_gnews_key
```

### Monitoring
- Monitor API usage and limits
- Set up alerts for API failures
- Track news relevance metrics

### Caching (Optional)
Consider implementing caching for production:
- Redis for API response caching
- CDN for static content
- Browser caching for images

## 🛠️ Customization

### Adding New Sources
To add more news sources, edit `src/services/newsApi.ts`:

```typescript
private readonly alternativeApis = [
  {
    name: 'YourAPI',
    url: 'https://your-api.com/endpoint',
    key: process.env.REACT_APP_YOUR_API_KEY || ''
  }
];
```

### Filtering Keywords
Modify cybersecurity keywords in `newsApi.ts`:

```typescript
private readonly cybersecurityKeywords = [
  'your-custom-keyword',
  'another-keyword'
];
```

### UI Customization
The component supports:
- Custom icons per news type
- Configurable refresh intervals
- Adjustable article limits
- Theme customization

## 📊 Analytics

Track news engagement:
- Click-through rates
- Most popular topics
- Source performance
- User interaction patterns

## 🐛 Troubleshooting

### Common Issues

1. **No news loading**:
   - Check API keys in `.env`
   - Verify internet connection
   - Check browser console for errors

2. **Rate limit exceeded**:
   - Wait for rate limit reset
   - Use alternative API keys
   - Implement caching

3. **Irrelevant news**:
   - Adjust keyword filters
   - Improve content scoring
   - Add source blacklists

### Debug Mode
Enable debug logging:
```typescript
// In newsApi.ts
console.log('API Response:', data);
```

## 📈 Future Enhancements

- AI-powered relevance scoring
- Sentiment analysis
- Threat level classification
- Custom news sources
- Real-time notifications
- News summarization
- Multi-language support

## 📞 Support

For issues with:
- **NewsAPI**: https://newsapi.org/docs
- **GNews**: https://gnews.io/docs
- **Component**: Check GitHub issues or create new one
