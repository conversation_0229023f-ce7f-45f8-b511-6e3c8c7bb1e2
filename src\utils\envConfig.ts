/**
 * Environment configuration utility
 * Maps environment variables to ensure compatibility
 */

// Helper function to get environment variables with fallbacks
const getEnvVar = (viteKey: string, reactKey: string): string | undefined => {
  return import.meta.env[viteKey] || import.meta.env[reactKey];
};

export const ENV_CONFIG = {
  // API Keys
  OPENAI_API_KEY: getEnvVar('VITE_OPENAI_API_KEY', 'REACT_APP_OPENAI_API_KEY'),
  NEWS_API_KEY: getEnvVar('VITE_NEWS_API_KEY', 'REACT_APP_NEWS_API_KEY'),
  GNEWS_API_KEY: getEnvVar('VITE_GNEWS_API_KEY', 'REACT_APP_GNEWS_API_KEY'),
  NEWSDATA_API_KEY: getEnvVar('VITE_NEWSDATA_API_KEY', 'REACT_APP_NEWSDATA_API_KEY'),
  
  // API URLs
  API_BASE_URL: import.meta.env.VITE_API_BASE_URL || 'https://aipt-be-server.onrender.com',
} as const;

export default ENV_CONFIG;
