import React, { useState, useEffect } from "react";
import { Vulnerability } from "../../types/types";
import {
  AlertTriangle,
  ChevronDown,
  ChevronUp,
  Info,
  ArrowRight,
  MessageSquare,
  Loader2,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useDashboard } from "../../context/DashboardContext";
import ENV_CONFIG from "../../utils/envConfig";

interface VulnerabilitiesTableProps {
  vulnerabilities?: Vulnerability[];
  onFilterChange?: (severity: string | null) => void;
  showAll?: boolean;
}

const VulnerabilitiesTable = React.memo(({
  vulnerabilities: propVulnerabilities,
  onFilterChange = () => {},
  showAll = false,
}: VulnerabilitiesTableProps) => {
  const navigate = useNavigate();
  const { vulnerabilities: contextVulnerabilities } = useDashboard();
  const [sortField, setSortField] = useState<string>("severity");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [selectedSeverity, setSelectedSeverity] = useState<string | null>(null);
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});
  const [displayedVulnerabilities, setDisplayedVulnerabilities] = useState<
    Vulnerability[]
  >([]);
  const [vulnerabilities, setVulnerabilities] = useState<Vulnerability[]>(
    propVulnerabilities || []
  );
  const [aiResponses, setAiResponses] = useState<Record<string, string>>({});
  const [loadingAI, setLoadingAI] = useState<Record<string, boolean>>({});

  useEffect(() => {
    if (!propVulnerabilities || propVulnerabilities.length === 0) {
      if (contextVulnerabilities && contextVulnerabilities.length > 0) {
        setVulnerabilities(
          contextVulnerabilities.map((v) => ({
            ...v,
            severity: (
              ["critical", "high", "medium", "low", "info"].includes(
                v.severity.toLowerCase()
              )
                ? v.severity.toLowerCase()
                : "info"
            ) as Vulnerability["severity"],
            evidence: v.evidence ?? [],
            solutions: v.solutions ?? [],
          }))
        );
      }
    } else {
      setVulnerabilities(
        propVulnerabilities.map((v) => ({
          ...v,
          severity: (
            ["critical", "high", "medium", "low", "info"].includes(
              v.severity.toLowerCase()
            )
              ? v.severity.toLowerCase()
              : "info"
          ) as Vulnerability["severity"],
        }))
      );
    }
  }, [propVulnerabilities, contextVulnerabilities]);

  useEffect(() => {
    if (propVulnerabilities && propVulnerabilities.length > 0) {
      setVulnerabilities(propVulnerabilities);
    }
  }, [propVulnerabilities]);

  useEffect(() => {
    handleFilterChange(null);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [vulnerabilities]);

  useEffect(() => {
    handleFilterChange(selectedSeverity);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortField, sortDirection, vulnerabilities]);

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const toggleRowExpanded = (id: string) => {
    setExpandedRows((prev) => {
      const newState = { ...prev };
      Object.keys(newState).forEach((key) => {
        if (key !== id) newState[key] = false;
      });
      newState[id] = !prev[id];
      return newState;
    });
  };

  const handleAskAI = async (vulnerability: Vulnerability) => {
    if (loadingAI[vulnerability.id]) return;

    setLoadingAI((prev) => ({ ...prev, [vulnerability.id]: true }));

    try {
      const prompt = `As a security expert, analyze this vulnerability and provide specific remediation steps:
      
      Vulnerability: ${vulnerability.name}
      Severity: ${vulnerability.severity}
      Endpoint: ${vulnerability.endpoint}
      Attack Vector: ${vulnerability.attackVector}
      Description: ${vulnerability.description}


      Please provide:
      1. A brief analysis of the risk
      2. Step-by-step remediation instructions
      3. Code examples where applicable
      4. Additional security best practices`;

      const apiKey = ENV_CONFIG.OPENAI_API_KEY;
      if (!apiKey) {
        throw new Error("OpenAI API key is missing");
      }

      const response = await fetch(
        "https://api.openai.com/v1/chat/completions",
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${apiKey}`,
          },
          body: JSON.stringify({
            model: "gpt-3.5-turbo",
            messages: [{ role: "user", content: prompt }],
            temperature: 0.7,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();
      const aiResponse =
        data.choices[0]?.message?.content ||
        "Unable to generate remediation steps.";

      setAiResponses((prev) => ({ ...prev, [vulnerability.id]: aiResponse }));
    } catch (error) {
      console.error("Error getting AI response:", error);
      setAiResponses((prev) => ({
        ...prev,
        [vulnerability.id]:
          "Sorry, I couldn't generate remediation steps at the moment. Please try again later.",
      }));
    } finally {
      setLoadingAI((prev) => ({ ...prev, [vulnerability.id]: false }));
    }
  };

  const handleFilterChange = (severity: string | null) => {
    setSelectedSeverity(severity);
    onFilterChange(severity);
    setExpandedRows({});

    let filtered = severity
      ? vulnerabilities.filter((v) => v.severity === severity)
      : vulnerabilities;

    filtered = [...filtered].sort((a, b) => {
      if (sortField === "severity") {
        const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
        const aValue =
          severityOrder[
            a.severity.toLowerCase() as keyof typeof severityOrder
          ] || 0;
        const bValue =
          severityOrder[
            b.severity.toLowerCase() as keyof typeof severityOrder
          ] || 0;
        return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
      }

      const aValue = a[sortField as keyof Vulnerability];
      const bValue = b[sortField as keyof Vulnerability];

      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortDirection === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      return 0;
    });

    setDisplayedVulnerabilities(showAll ? filtered : filtered.slice(0, 5));
  };

  useEffect(() => {
    handleFilterChange(selectedSeverity);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortField, sortDirection, vulnerabilities]);

  return (
    <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-3 sm:p-4 lg:p-6 h-full flex flex-col">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-3 sm:mb-4 space-y-3 sm:space-y-0">
        <div className="flex items-center gap-2">
          <h3 className="text-base sm:text-lg lg:text-xl font-semibold text-neutral-900 dark:text-white">
            Top Vulnerabilities
          </h3>
          <div className="tooltip-container relative group">
            <Info className="w-4 h-4 sm:w-5 sm:h-5 text-neutral-400 hover:text-neutral-600 dark:text-neutral-300 dark:hover:text-neutral-100 cursor-help" />
            <div className="tooltip absolute left-0 top-8 w-64 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block dark:text-neutral-200">
              Highlights the most critical issues sorted by CVSS score: High,
              Medium, Low.
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-1 sm:space-x-2 overflow-x-auto pb-1">
          <button
            className={`text-xs sm:text-sm px-2 py-1 rounded-md transition-colors whitespace-nowrap ${
              selectedSeverity === null
                ? "bg-[#00457f] text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-200"
            }`}
            onClick={() => handleFilterChange(null)}
          >
            All
          </button>
          <button
            className={`text-xs sm:text-sm px-2 py-1 rounded-md transition-colors whitespace-nowrap ${
              selectedSeverity === "critical"
                ? "bg-[#7f1d1d] text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-200"
            }`}
            onClick={() => handleFilterChange("critical")}
          >
            <span className="hidden sm:inline">Critical</span>
            <span className="sm:hidden">Crit</span>
          </button>
          <button
            className={`text-xs sm:text-sm px-2 py-1 rounded-md transition-colors whitespace-nowrap ${
              selectedSeverity === "high"
                ? "bg-[#dc2626] text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-200"
            }`}
            onClick={() => handleFilterChange("high")}
          >
            High
          </button>
          <button
            className={`text-xs sm:text-sm px-2 py-1 rounded-md transition-colors whitespace-nowrap ${
              selectedSeverity === "medium"
                ? "bg-[#fbbf24] text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-200"
            }`}
            onClick={() => handleFilterChange("medium")}
          >
            <span className="hidden sm:inline">Medium</span>
            <span className="sm:hidden">Med</span>
          </button>
          <button
            className={`text-xs sm:text-sm px-2 py-1 rounded-md transition-colors whitespace-nowrap ${
              selectedSeverity === "low"
                ? "bg-[#16a34a] text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-200"
            }`}
            onClick={() => handleFilterChange("low")}
          >
            <span className="hidden sm:inline">Low</span>
            <span className="sm:hidden">Low</span>     
          </button>
          <button
            className={`text-xs sm:text-sm px-2 py-1 rounded-md transition-colors whitespace-nowrap ${
              selectedSeverity === "info"
                ? "bg-[#0ea5e9] text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-200"
            }`}
            onClick={() => handleFilterChange("info")}
          >
            <span className="hidden sm:inline">info</span>
            <span className="sm:hidden">info</span>     
          </button>
          {/* <button className="hidden sm:inline-block border border-neutral-400 dark:border-neutral-600 rounded-md px-1.5 py-0.5 text-xs text-neutral-700 dark:text-neutral-300 hover:border-neutral-500 transition-colors whitespace-nowrap">
            View Details
          </button> */}
        </div>
      </div>

      <div className="flex-1 overflow-hidden">
        <div
          className="h-full overflow-x-auto overflow-y-auto hide-scrollbar"
          style={{ maxHeight: showAll ? "none" : "300px" }}
        >
          <table className="data-table w-full table-fixed min-w-[600px]">
            <thead className="sticky top-0 z-10 bg-neutral-200 dark:bg-neutral-900">
              <tr>
                <th className="w-[40px] border-b border-neutral-300 dark:border-neutral-700 bg-neutral-200 dark:bg-neutral-900"></th>
                <th
                  className="w-[35%] sm:w-[40%] lg:w-[45%] cursor-pointer font-bold text-neutral-800 dark:text-white border-b border-neutral-300 dark:border-neutral-700 bg-neutral-200 dark:bg-neutral-900 text-xs sm:text-sm"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center">
                    <span className="hidden sm:inline">Vulnerability</span>
                    <span className="sm:hidden">Vuln</span>
                    {sortField === "name" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ) : (
                        <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ))}
                  </div>
                </th>
                <th
                  className="w-[15%] cursor-pointer font-bold text-neutral-800 dark:text-white border-b border-neutral-300 dark:border-neutral-700 bg-neutral-200 dark:bg-neutral-900 text-xs sm:text-sm"
                  onClick={() => handleSort("severity")}
                >
                  <div className="flex items-center">
                    <span className="hidden sm:inline">Severity</span>
                    <span className="sm:hidden">Sev</span>
                    {sortField === "severity" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ) : (
                        <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ))}
                  </div>
                </th>
                <th
                  className="w-[25%] sm:w-[22%] lg:w-[20%] cursor-pointer font-bold text-neutral-800 dark:text-white border-b border-neutral-300 dark:border-neutral-700 bg-neutral-200 dark:bg-neutral-900 text-xs sm:text-sm"
                  onClick={() => handleSort("endpoint")}
                >
                  <div className="flex items-center">
                    Endpoint
                    {sortField === "endpoint" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ) : (
                        <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ))}
                  </div>
                </th>
                <th
                  className="w-[25%] sm:w-[23%] lg:w-[20%] cursor-pointer font-bold text-neutral-800 dark:text-white border-b border-neutral-300 dark:border-neutral-700 bg-neutral-200 dark:bg-neutral-900 text-xs sm:text-sm"
                  onClick={() => handleSort("attackVector")}
                >
                  <div className="flex items-center">
                    <span className="hidden sm:inline">Attack Vector</span>
                    <span className="sm:hidden">Vector</span>
                    {sortField === "attackVector" &&
                      (sortDirection === "asc" ? (
                        <ChevronUp className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ) : (
                        <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 ml-1" />
                      ))}
                  </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <AnimatePresence>
                {displayedVulnerabilities.length > 0 ? (
                  displayedVulnerabilities.map((vuln) => (
                    <React.Fragment key={vuln.id}>
                      <motion.tr
                        className={`cursor-pointer transition-colors ${
                          expandedRows[vuln.id]
                            ? "bg-neutral-50 dark:bg-neutral-800 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                            : "hover:bg-neutral-100 dark:hover:bg-neutral-800"
                        }`}
                        onClick={() => toggleRowExpanded(vuln.id)}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        exit={{ opacity: 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        <td className="text-center w-[40px] bg-inherit">
                          <motion.div
                            initial={false}
                            animate={{
                              rotate: expandedRows[vuln.id] ? 180 : 0,
                            }}
                            transition={{ duration: 0.2 }}
                          >
                            <ChevronDown className="w-3 h-3 sm:w-4 sm:h-4 inline-block dark:text-neutral-200" />
                          </motion.div>
                        </td>
                        <td className="w-[35%] sm:w-[40%] lg:w-[45%] truncate dark:text-neutral-100 bg-inherit text-xs sm:text-sm">
                          <span className="block truncate" title={vuln.name}>
                            {vuln.name}
                          </span>
                        </td>
                        <td className="w-[15%] bg-inherit">
                          <span
                            className={`severity-badge severity-${vuln.severity.toLowerCase()} text-xs ${
                              vuln.severity.toLowerCase() === "info"
                                ? "bg-sky-100 text-sky-700 dark:bg-sky-900 dark:text-sky-300 border border-sky-200 dark:border-sky-800"
                                : ""
                            }`}
                          >
                            <span className="hidden sm:inline">{vuln.severity}</span>
                            <span className="sm:hidden">{vuln.severity.charAt(0).toUpperCase()}</span>
                          </span>
                        </td>
                        <td className="w-[25%] sm:w-[22%] lg:w-[20%] bg-inherit">
                          <code className="text-xs bg-neutral-100 dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 px-1 py-0.5 rounded truncate block max-w-full" title={vuln.endpoint}>
                            {vuln.endpoint}
                          </code>
                        </td>
                        <td className="w-[25%] sm:w-[23%] lg:w-[20%] truncate dark:text-neutral-100 bg-inherit text-xs sm:text-sm" title={vuln.attackVector}>
                          {vuln.attackVector}
                        </td>
                      </motion.tr>
                      <AnimatePresence>
                        {expandedRows[vuln.id] && (
                          <motion.tr
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                          >
                            <td
                              colSpan={5}
                              className="bg-neutral-100 dark:bg-neutral-800 p-0"
                            >
                              <motion.div
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -10 }}
                                transition={{ duration: 0.2, delay: 0.1 }}
                                className="p-3 sm:p-4 lg:p-6"
                              >
                                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                                  <div className="space-y-3 sm:space-y-4">
                                    <div>
                                      <h4 className="text-xs sm:text-sm font-semibold text-neutral-700 dark:text-neutral-200 mb-2">
                                        Description
                                      </h4>
                                      <p className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-300 leading-relaxed">
                                        {vuln.description}
                                      </p>
                                    </div>
                                    <div>
                                      <h4 className="text-xs sm:text-sm font-semibold text-neutral-700 dark:text-neutral-200 mb-2">
                                        Compliance Mapping
                                      </h4>
                                      <div className="flex flex-wrap gap-1 sm:gap-2">
                                        {vuln.complianceMapping.map(
                                          (mapping, i) => (
                                            <span
                                              key={i}
                                              className="bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-200 text-xs font-medium px-2 py-1 rounded-full border border-primary-100 dark:border-primary-800"
                                            >
                                              {mapping}
                                            </span>
                                          )
                                        )}
                                      </div>
                                    </div>
                                    <div>
                                      <h4 className="text-xs sm:text-sm font-semibold text-neutral-700 dark:text-neutral-200 mb-2">
                                        Evidence
                                      </h4>
                                      {Array.isArray(vuln.evidence) ? (
                                        <ul className="list-disc pl-4 text-xs sm:text-sm text-neutral-600 dark:text-neutral-300">
                                          {vuln.evidence.map((ev, idx) => (
                                            <li key={idx}>{ev}</li>
                                          ))}
                                        </ul>
                                      ) : (
                                        <p className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-300">
                                          {vuln.evidence || "No evidence provided."}
                                        </p>
                                      )}
                                    </div>
                                    <div>
                                      <h4 className="text-xs sm:text-sm font-semibold text-neutral-700 dark:text-neutral-200 mb-2">
                                        Solutions
                                      </h4>
                                      {Array.isArray(vuln.solutions) ? (
                                        <ul className="list-disc pl-4 text-xs sm:text-sm text-neutral-600 dark:text-neutral-300">
                                          {vuln.solutions.map((sol, idx) => (
                                            <li key={idx}>{sol}</li>
                                          ))}
                                        </ul>
                                      ) : (
                                        <p className="text-xs sm:text-sm text-neutral-600 dark:text-neutral-300">
                                          {vuln.solutions || "No solutions provided."}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                  <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-3 sm:p-4 relative">
                                    <div className="absolute top-0 right-0 bg-primary-500 text-white text-xs py-1 px-2 rounded-bl-lg rounded-tr-lg">
                                      AI Assistance
                                    </div>
                                    {!aiResponses[vuln.id] ? (
                                      <div>
                                        <h4 className="text-xs sm:text-sm font-semibold text-primary-700 dark:text-primary-200 mb-3">
                                          Need Help?
                                        </h4>
                                        <button
                                          onClick={() => handleAskAI(vuln)}
                                          disabled={loadingAI[vuln.id]}
                                          className="w-full bg-primary-50 dark:bg-primary-900 hover:bg-primary-100 dark:hover:bg-primary-800 text-primary-700 dark:text-primary-200 font-medium py-2 sm:py-3 px-3 sm:px-4 rounded-lg transition-colors flex items-center justify-center gap-2 disabled:opacity-50 text-xs sm:text-sm"
                                        >
                                          {loadingAI[vuln.id] ? (
                                            <>
                                              <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 animate-spin" />
                                              <span className="hidden sm:inline">Analyzing...</span>
                                              <span className="sm:hidden">...</span>
                                            </>
                                          ) : (
                                            <>
                                              <MessageSquare className="w-3 h-3 sm:w-4 sm:h-4" />
                                              <span className="hidden sm:inline">Ask AI for Remediation Steps</span>
                                              <span className="sm:hidden">Ask AI</span>
                                            </>
                                          )}
                                        </button>
                                      </div>
                                    ) : (
                                      <div className="prose prose-sm max-w-none">
                                        <div className="whitespace-pre-wrap text-xs sm:text-sm text-neutral-700 dark:text-neutral-200 max-h-40 sm:max-h-60 overflow-y-auto">
                                          {aiResponses[vuln.id]}
                                        </div>
                                        <button
                                          onClick={() =>
                                            setAiResponses((prev) => ({
                                              ...prev,
                                              [vuln.id]: "",
                                            }))
                                          }
                                          className="mt-3 sm:mt-4 text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 text-xs sm:text-sm font-medium"
                                        >
                                          Ask Again
                                        </button>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              </motion.div>
                            </td>
                          </motion.tr>
                        )}
                      </AnimatePresence>
                    </React.Fragment>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={5}
                      className="text-center py-8 bg-neutral-100 dark:bg-neutral-900"
                    >
                      <div className="flex flex-col items-center text-neutral-500 dark:text-neutral-300">
                        <AlertTriangle className="w-6 h-6 mb-2" />
                        <p>No vulnerabilities found</p>
                      </div>
                    </td>
                  </tr>
                )}
              </AnimatePresence>
            </tbody>
          </table>
        </div>
      </div>

      {!showAll && vulnerabilities.length > 5 && (
        <div className="mt-4 flex justify-center border-t border-neutral-100 dark:border-neutral-800 pt-4">
          <button
            onClick={() => navigate("/vulnerabilities?tab=vulnerabilities")}
            className="flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm"
          >
            Show All
            <ArrowRight className="w-4 h-4 ml-1" />
          </button>
        </div>
      )}
    </div>
  );
});

export default VulnerabilitiesTable;

