import React from 'react';
import { Link } from 'react-router-dom';

const NotFound: React.FC = () => {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-[#00457F]/50 px-4 py-8">
      <div className="flex flex-col items-center w-full max-w-2xl">
        <h1 className="text-4xl sm:text-5xl font-bold text-neutral-900 dark:text-white mb-2 text-center">
          404 Not <span className="text-[#00457F]">Found.</span>
        </h1>
        <p className="text-base sm:text-lg text-neutral-600 dark:text-neutral-300 mb-8 text-center max-w-xl">
          Our team of seasoned security specialists works tirelessly to protect organizations edge technology.
        </p>
        <div className="relative flex items-center justify-center mb-6">
          <span className="text-[120px] sm:text-[160px] md:text-[200px] font-extrabold text-[#bfc9db] dark:text-[#3a3a5a] opacity-60 select-none leading-none tracking-tight">
            404
          </span>
          
        </div>
        <h2 className="text-2xl sm:text-3xl font-bold text-neutral-900 dark:text-white mb-2 text-center">
          Page Not Found
        </h2>
        <p className="text-neutral-600 dark:text-neutral-300 mb-8 text-center max-w-lg">
          Oops! The page you’re looking for doesn’t exist. It seems you’ve taken a wrong turn or followed a link that’s no longer active. But don’t worry—you’re not lost!<br />
          Head back to our homepage or use the menu above to find what you’re looking for. Our support team is always here to help.
        </p>
        <Link
          to="/"
          className="mt-2 px-8 py-3 rounded-lg bg-[#00457F] hover:bg-[#00457F]/50 text-white font-semibold text-lg shadow transition-colors focus:outline-none focus:ring-2 focus:ring-[#00457F] focus:ring-offset-0.5"
        >
          Back To Home &rarr;
        </Link>
      </div>
    </div>
  );
};

export default NotFound;