
import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useDashboard } from '../context/DashboardContext';
import { ArrowLeft, Info } from 'lucide-react';
import { Vulnerability } from '../types/types';

const AllVulnerabilities: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { vulnerabilities, endpoints } = useDashboard();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'vulnerabilities' | 'endpoints'>('vulnerabilities');

  // Set initial tab based on URL parameter
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam === 'endpoints') {
      setActiveTab('endpoints');
    } else {
      setActiveTab('vulnerabilities');
    }
  }, [searchParams]);

  // Reset search and filters when switching tabs
  const handleTabChange = (tab: 'vulnerabilities' | 'endpoints') => {
    setActiveTab(tab);
    setSearchTerm('');
    setSelectedSeverity(null);
    // Update URL parameter
    navigate(`/vulnerabilities?tab=${tab}`, { replace: true });
  };

  // Filter vulnerabilities based on search term and severity
  const filteredVulnerabilities = vulnerabilities.filter(vuln => {
    const matchesSearch = searchTerm === '' ||
      vuln.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vuln.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vuln.endpoint.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vuln.attackVector.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSeverity = selectedSeverity === null || vuln.severity === selectedSeverity;

    return matchesSearch && matchesSeverity;
  });

  // Filter endpoints based on search term and severity
  const filteredEndpoints = endpoints.filter(endpoint => {
    const matchesSearch = searchTerm === '' ||
      endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
      endpoint.vulnerabilityType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      endpoint.method.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesSeverity = selectedSeverity === null || endpoint.severity === selectedSeverity;

    return matchesSearch && matchesSeverity;
  });

  const getSeverityClass = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'text-purple-600 dark:text-purple-400';
      case 'high': return 'text-danger-600 dark:text-danger-400';
      case 'medium': return 'text-warning-600 dark:text-warning-400';
      case 'low': return 'text-success-600 dark:text-success-400';
      default: return 'text-neutral-600 dark:text-neutral-300';
    }
  };

  const getSeverityBadge = (severity: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    switch (severity.toLowerCase()) {
      case 'critical': return `${baseClasses} bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300`;
      case 'high': return `${baseClasses} bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300`;
      case 'medium': return `${baseClasses} bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300`;
      case 'low': return `${baseClasses} bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300`;
      default: return `${baseClasses} bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300`;
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate(-1)}
          className="flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mr-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          Back
        </button>
        <div>
          <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">All Vulnerabilities & Security Issues</h1>
          <p className="text-neutral-600 dark:text-neutral-300">Complete list of detected vulnerabilities and security issues</p>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-800 mb-6">
        <div className="flex border-b border-neutral-200 dark:border-neutral-700">
          <button
            onClick={() => handleTabChange('vulnerabilities')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === 'vulnerabilities'
                ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20'
                : 'text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100'
            }`}
          >
            Vulnerabilities ({vulnerabilities.length})
          </button>
          <button
            onClick={() => handleTabChange('endpoints')}
            className={`px-6 py-3 text-sm font-medium transition-colors ${
              activeTab === 'endpoints'
                ? 'text-primary-600 border-b-2 border-primary-600 bg-primary-50 dark:bg-primary-900/20'
                : 'text-neutral-600 dark:text-neutral-300 hover:text-neutral-800 dark:hover:text-neutral-100'
            }`}
          >
            Vulnerable Endpoints ({endpoints.length})
          </button>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-4">
        {/* Header with search and filters */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold dark:text-white">
              {activeTab === 'vulnerabilities' ? 'Vulnerabilities & Security Issues' : 'Vulnerable Endpoints'}
            </h3>
            <div className="tooltip-container relative group">
              <Info className="w-5 h-5 text-neutral-400 hover:text-neutral-600 dark:text-neutral-300 dark:hover:text-neutral-100 cursor-help" />
              <div className="tooltip absolute right-0 top-8 w-64 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block dark:text-neutral-200">
                {activeTab === 'vulnerabilities'
                  ? 'Security vulnerabilities and issues detected in your application. Use filters and search to find specific vulnerabilities.'
                  : 'API endpoints and web paths that have been identified as vulnerable to security attacks.'
                }
              </div>
            </div>
          </div>

          <div className="relative">
            <input
              type="text"
              className="text-sm border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder={activeTab === 'vulnerabilities' ? "Search vulnerabilities..." : "Search endpoints..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Severity Filters */}
        <div className="flex flex-wrap gap-2 mb-4">
          <button
            className={`text-xs px-3 py-1 rounded-md transition-colors ${
              selectedSeverity === null
                ? 'bg-primary-600 text-white'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-300'
            }`}
            onClick={() => setSelectedSeverity(null)}
          >
            All ({activeTab === 'vulnerabilities' ? vulnerabilities.length : endpoints.length})
          </button>
          <button
            className={`text-xs px-3 py-1 rounded-md transition-colors ${
              selectedSeverity === 'critical'
                ? 'bg-purple-600 text-white'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-300'
            }`}
            onClick={() => setSelectedSeverity('critical')}
          >
            Critical ({activeTab === 'vulnerabilities'
              ? vulnerabilities.filter(v => v.severity === 'critical').length
              : endpoints.filter(e => e.severity === 'critical').length})
          </button>
          <button
            className={`text-xs px-3 py-1 rounded-md transition-colors ${
              selectedSeverity === 'high'
                ? 'bg-red-600 text-white'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-300'
            }`}
            onClick={() => setSelectedSeverity('high')}
          >
            High ({activeTab === 'vulnerabilities'
              ? vulnerabilities.filter(v => v.severity === 'high').length
              : endpoints.filter(e => e.severity === 'high').length})
          </button>
          <button
            className={`text-xs px-3 py-1 rounded-md transition-colors ${
              selectedSeverity === 'medium'
                ? 'bg-yellow-600 text-white'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-300'
            }`}
            onClick={() => setSelectedSeverity('medium')}
          >
            Medium ({activeTab === 'vulnerabilities'
              ? vulnerabilities.filter(v => v.severity === 'medium').length
              : endpoints.filter(e => e.severity === 'medium').length})
          </button>
          <button
            className={`text-xs px-3 py-1 rounded-md transition-colors ${
              selectedSeverity === 'low'
                ? 'bg-green-600 text-white'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-600 dark:text-neutral-300'
            }`}
            onClick={() => setSelectedSeverity('low')}
          >
            Low ({activeTab === 'vulnerabilities'
              ? vulnerabilities.filter(v => v.severity === 'low').length
              : endpoints.filter(e => e.severity === 'low').length})
          </button>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="data-table w-full">
            <thead>
              <tr>
                {activeTab === 'vulnerabilities' ? (
                  <>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Vulnerability Name</th>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Severity</th>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Endpoint</th>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Attack Vector</th>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Description</th>
                  </>
                ) : (
                  <>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Method</th>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Endpoint Path</th>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Vulnerability Type</th>
                    <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Severity</th>
                  </>
                )}
              </tr>
            </thead>
            <tbody>
              {activeTab === 'vulnerabilities' ? (
                filteredVulnerabilities.length > 0 ? (
                  filteredVulnerabilities.map((vuln) => (
                    <tr
                      key={vuln.id}
                      className="transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-800"
                    >
                      <td className="font-medium text-neutral-900 dark:text-neutral-100 bg-inherit">{vuln.name}</td>
                      <td className="bg-inherit">
                        <span className={getSeverityBadge(vuln.severity)}>
                          {vuln.severity.charAt(0).toUpperCase() + vuln.severity.slice(1)}
                        </span>
                      </td>
                      <td className="bg-inherit">
                        <code className="text-xs bg-neutral-100 dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 px-2 py-1 rounded">
                          {vuln.endpoint}
                        </code>
                      </td>
                      <td className="text-neutral-900 dark:text-neutral-100 bg-inherit text-sm">{vuln.attackVector}</td>
                      <td className="text-neutral-900 dark:text-neutral-100 bg-inherit text-sm max-w-xs truncate" title={vuln.description}>
                        {vuln.description}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="text-center py-4 text-neutral-500 dark:text-neutral-300 bg-neutral-100 dark:bg-neutral-900">
                      No vulnerabilities found matching search criteria
                    </td>
                  </tr>
                )
              ) : (
                filteredEndpoints.length > 0 ? (
                  filteredEndpoints.map((endpoint) => (
                    <tr
                      key={endpoint.id}
                      className="transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-800"
                    >
                      <td className="bg-inherit">
                        <span className={`inline-block px-2 py-1 text-xs font-medium rounded ${
                          endpoint.method === 'GET' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                          endpoint.method === 'POST' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                          endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                          endpoint.method === 'DELETE' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                          'bg-neutral-100 text-neutral-800 dark:bg-neutral-800 dark:text-neutral-200'
                        }`}>
                          {endpoint.method}
                        </span>
                      </td>
                      <td className="font-mono text-sm text-neutral-900 dark:text-neutral-100 bg-inherit">{endpoint.path}</td>
                      <td className="text-neutral-900 dark:text-neutral-100 bg-inherit text-sm">{endpoint.vulnerabilityType}</td>
                      <td className="bg-inherit">
                        <span className={getSeverityBadge(endpoint.severity)}>
                          {endpoint.severity.charAt(0).toUpperCase() + endpoint.severity.slice(1)}
                        </span>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={4} className="text-center py-4 text-neutral-500 dark:text-neutral-300 bg-neutral-100 dark:bg-neutral-900">
                      No vulnerable endpoints found matching search criteria
                    </td>
                  </tr>
                )
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AllVulnerabilities;