import React, { useState, useEffect, useRef } from 'react';
import { BellRing, User, Search, ChevronDown, Scan, Menu, X, LogOut, Settings, UserCircle, Bell, Clock, CheckCircle, AlertCircle, Moon, Sun, Home, AlertOctagon, FileText } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useDashboard } from '../../context/DashboardContext';
import SearchModal from './SearchModal';
import { motion, AnimatePresence } from 'framer-motion';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import DefendlyFullLogo from '../../assets/defendly full logo.svg';
import DefendlyFullLogoWhite from '../../assets/defendly full logo white.svg'; // Add this import

interface NavbarProps {
  onSearch: (query: string) => void;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  type: 'success' | 'warning' | 'info';
  read: boolean;
}

const Navbar: React.FC<NavbarProps> = ({ onSearch }) => {
  const [showOrgDropdown, setShowOrgDropdown] = useState(false);
  const [showProjectDropdown, setShowProjectDropdown] = useState(false);
  const [showSearchModal, setShowSearchModal] = useState(false);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const orgDropdownRef = useRef<HTMLDivElement>(null);
  const projectDropdownRef = useRef<HTMLDivElement>(null);
  const profileDropdownRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { stats } = useDashboard();
  const { theme, toggleTheme } = useTheme();
  const { logout } = useAuth();

  // Mock notifications data
  const notifications: Notification[] = [
    {
      id: '1',
      title: 'Scan Completed',
      message: 'Security scan for api.example.com completed successfully',
      timestamp: '2 minutes ago',
      type: 'success',
      read: false
    },
    {
      id: '2',
      title: 'High Severity Alert',
      message: 'New SQL injection vulnerability detected in login endpoint',
      timestamp: '1 hour ago',
      type: 'warning',
      read: false
    },
    {
      id: '3',
      title: 'System Update',
      message: 'New security features have been added to your dashboard',
      timestamp: '3 hours ago',
      type: 'info',
      read: true
    }
  ];
  
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target as Node)) {
        setShowOrgDropdown(false);
      }
      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target as Node)) {
        setShowProjectDropdown(false);
      }
      if (profileDropdownRef.current && !profileDropdownRef.current.contains(event.target as Node)) {
        setShowProfileDropdown(false);
      }
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setShowNotifications(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const organizations = [
    { id: 1, name: 'Acme Corp' },
    { id: 2, name: 'Stark Industries' },
    { id: 3, name: 'Wayne Enterprises' },
    { id: 4, name: 'Bug Hunters' }
  ];

  const projects = [
    { id: 1, name: 'Bits Crunch' },
    { id: 2, name: 'Clinigma' },
    { id: 3, name: 'Glesec' }
  ];

  const getGrade = (score: number) => {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  };



  const getScoreColor = (score: number) => {
    if (score >= 80) return '#4CAF50';
    if (score >= 60) return '#FFC107';
    if (score >= 40) return '#FF9800';
    return '#F44336';
  };

  const renderScoreCard = (score: number, title: string, isGrade = false) => {
    // Get detailed tooltip content based on the metric
    const getTooltipContent = () => {
      switch (title) {
        case "Cyber Hygiene Score":
          return (
            <span>
              <b>Cyber Hygiene Score:</b> {score}/100
            </span>
          );
        case "Assets Risk Rating":
          return (
            <span>
              <b>Assets Risk Rating:</b> {score}/5
            </span>
          );
        case "Threat Intelligence Score":
          return (
            <span>
              <b>Threat Intelligence Score:</b> {score}/100
            </span>
          );
        case "Attack Surface Index":
          return (
            <span>
              <b>Open Ports:</b> {stats.openPorts}
            </span>
          );
        default:
          return <span>{title}</span>;
      }
    };

    // Always show the score for Assets Risk Rating, not the grade
    const displayValue =
      title === "Assets Risk Rating"
        ? score
        : title === "Attack Surface Index"
        ? stats.openPorts // Show openPorts value for Attack Surface Index
        : isGrade
        ? getGrade(score)
        : score;

    return (
      <div className="tooltip-container relative group">
        <div
          className="px-3 py-1 rounded-[500px] border"
          style={{ borderColor: getScoreColor(score) }}
        >
          <span className="font-semibold text-black dark:text-white">{displayValue}</span>
        </div>
        <div className="tooltip absolute left-0 top-8 bg-white dark:bg-neutral-900 p-3 rounded shadow-lg text-sm z-10 hidden group-hover:block w-64 text-black dark:text-white whitespace-pre-line">
          {getTooltipContent()}
        </div>
      </div>
    );
  };

  const renderNotificationIcon = (type: 'success' | 'warning' | 'info') => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-success-500" />;
      case 'warning':
        return <AlertCircle className="w-5 h-5 text-warning-500" />;
      case 'info':
        return <Bell className="w-5 h-5 text-primary-500" />;
    }
  };
  
  return (
    <>
      <nav className="fixed top-0 left-0 right-0 h-14 sm:h-16 lg:h-18 bg-white dark:bg-[#202020] shadow-sm z-40 px-2 sm:px-4 lg:px-6">
        <div className="flex items-center justify-between h-full">
          <div className="flex items-center">
            {/* Responsive Logo */}
            <img
              src={theme === 'dark' ? DefendlyFullLogoWhite : DefendlyFullLogo}
              alt="Defendly Full Logo"
              className="h-6 sm:h-8 lg:h-10 w-auto mr-3 sm:mr-4 lg:mr-6"
              style={{
                maxHeight: 40,
              }}
            />
            <div className="hidden xl:flex items-center space-x-2 lg:space-x-3">
              {renderScoreCard(stats.cyberHygieneScore, "Cyber Hygiene Score")}
              {renderScoreCard(stats.vendorRiskRating, "Assets Risk Rating")} {/* Remove 'true' for isGrade */}
              {renderScoreCard(stats.threatScore, "Threat Intelligence Score")}
              {renderScoreCard(stats.attackSurfaceIndex, "Attack Surface Index")}
            </div>
          </div>
          
          <div className="hidden lg:flex flex-1 max-w-sm xl:max-w-xl mx-3 lg:mx-6">
            <button
              onClick={() => setShowSearchModal(true)}
              className="w-full flex items-center px-3 lg:px-4 py-1.5 lg:py-2 text-xs sm:text-sm text-neutral-700 dark:text-neutral-200 bg-neutral-50 dark:bg-black border border-neutral-300 dark:border-neutral-700 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-700 transition-colors"
            >
              <Search className="w-3 h-3 lg:w-4 lg:h-4 text-neutral-500 mr-2 lg:mr-3" />
              <span className="text-neutral-500 dark:text-neutral-400 truncate">Ask about vulnerabilities...</span>
            </button>
          </div>
          
          <div className="hidden lg:flex items-center space-x-2 xl:space-x-4">
            {/* Organization Dropdown */}
            <div className="relative" ref={orgDropdownRef}>
              <button
                className={`flex items-center space-x-1 xl:space-x-2 px-2 xl:px-3 py-1.5 xl:py-2 text-xs xl:text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md transition-colors ${showOrgDropdown ? 'bg-neutral-100 dark:bg-neutral-800' : ''}`}
                onClick={() => setShowOrgDropdown(!showOrgDropdown)}
              >
                <span className="hidden xl:inline">Organization</span>
                <span className="xl:hidden">Org</span>
                <ChevronDown className={`w-3 h-3 xl:w-4 xl:h-4 transition-transform ${showOrgDropdown ? 'transform rotate-180' : ''}`} />
              </button>
              <AnimatePresence>
                {showOrgDropdown && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-48 bg-white dark:bg-neutral-900 rounded-md shadow-lg border border-neutral-200 dark:border-neutral-700 py-1 z-50"
                  >
                    {organizations.map(org => (
                      <button
                        key={org.id}
                        className="block w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                        onClick={() => setShowOrgDropdown(false)}
                      >
                        {org.name}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Targets Dropdown */}
            <div className="relative" ref={projectDropdownRef}>
              <button
                className={`flex items-center space-x-1 xl:space-x-2 px-2 xl:px-3 py-1.5 xl:py-2 text-xs xl:text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md transition-colors ${showProjectDropdown ? 'bg-neutral-100 dark:bg-neutral-800' : ''}`}
                onClick={() => setShowProjectDropdown(!showProjectDropdown)}
              >
                <span>Targets</span>
                <ChevronDown className={`w-3 h-3 xl:w-4 xl:h-4 transition-transform ${showProjectDropdown ? 'transform rotate-180' : ''}`} />
              </button>
              <AnimatePresence>
                {showProjectDropdown && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-48 bg-white dark:bg-neutral-900 rounded-md shadow-lg border border-neutral-200 dark:border-neutral-700 py-1 z-50"
                  >
                    {projects.map(project => (
                      <button
                        key={project.id}
                        className="block w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                        onClick={() => setShowProjectDropdown(false)}
                      >
                        {project.name}
                      </button>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>

            {/* Scan Now Button */}
            <button
              onClick={() => navigate('/assessment')}
              className="flex items-center space-x-1 xl:space-x-2 px-2 xl:px-4 py-1.5 xl:py-2 bg-[#00457F] text-white rounded-md hover:bg-[#00345c] transition-colors"
            >
              <Scan className="w-3 h-3 xl:w-4 xl:h-4" />
              <span className="text-xs xl:text-sm font-medium hidden lg:inline">Scan Now</span>
              <span className="text-xs xl:text-sm font-medium lg:hidden">Scan</span>
            </button>

            {/* Dark Mode Toggle (NAVBAR) */}
            <button
              onClick={toggleTheme}
              className="p-1.5 xl:p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
              aria-label="Toggle dark mode"
            >
              {theme === 'dark' ? (
                <Sun className="w-4 h-4 xl:w-5 xl:h-5 text-neutral-600 dark:text-neutral-300" />
              ) : (
                <Moon className="w-4 h-4 xl:w-5 xl:h-5 text-neutral-600 dark:text-neutral-300" />
              )}
            </button>

            {/* Notifications */}
            <div className="relative" ref={notificationsRef}>
              <button
                className="p-1.5 xl:p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-800 relative transition-colors"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                <BellRing className="w-4 h-4 xl:w-5 xl:h-5 text-neutral-600 dark:text-neutral-300" />
                <span className="absolute top-0 right-0 w-2 h-2 bg-danger-500 rounded-full"></span>
              </button>
              <AnimatePresence>
                {showNotifications && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-80 bg-white dark:bg-neutral-900 rounded-lg shadow-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden z-50"
                  >
                    <div className="p-4 border-b border-neutral-200 dark:border-neutral-700">
                      <h3 className="text-lg font-semibold text-black dark:text-white">Notifications</h3>
                    </div>
                    <div className="max-h-96 overflow-y-auto">
                      {notifications.map(notification => (
                        <div
                          key={notification.id}
                          className={`p-4 border-b border-neutral-100 dark:border-neutral-800 hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-colors ${
                            !notification.read ? 'bg-neutral-50 dark:bg-neutral-800' : ''
                          }`}
                        >
                          <div className="flex items-start gap-3">
                            {renderNotificationIcon(notification.type)}
                            <div className="flex-1">
                              <h4 className="text-sm font-medium text-neutral-800 dark:text-neutral-100">{notification.title}</h4>
                              <p className="text-sm text-neutral-600 dark:text-neutral-300 mt-1">{notification.message}</p>
                              <div className="flex items-center mt-2 text-xs text-neutral-500 dark:text-neutral-400">
                                <Clock className="w-3 h-3 mr-1" />
                                {notification.timestamp}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="p-3 bg-neutral-50 dark:bg-neutral-800 border-t border-neutral-200 dark:border-neutral-700">
                      <button className="w-full text-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium">
                        View All Notifications
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            
            {/* Profile Dropdown */}
            <div className="relative" ref={profileDropdownRef}>
              <button
                className="p-1.5 xl:p-2 rounded-full hover:bg-neutral-100 dark:hover:bg-neutral-800 cursor-pointer transition-colors"
                onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              >
                <User className="w-4 h-4 xl:w-5 xl:h-5 text-neutral-600 dark:text-neutral-300" />
              </button>
              <AnimatePresence>
                {showProfileDropdown && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="absolute right-0 mt-2 w-48 bg-white dark:bg-neutral-900 rounded-lg shadow-lg border border-neutral-200 dark:border-neutral-700 overflow-hidden z-50"
                  >
                    <div className="py-2">
                      <button
                        onClick={() => {
                          setShowProfileDropdown(false);
                          navigate('/profile');
                        }}
                        className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                      >
                        <UserCircle className="w-4 h-4 mr-3" />
                        Profile
                      </button>
                      <button
                        onClick={() => {
                          setShowProfileDropdown(false);
                          navigate('/settings');
                        }}
                        className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                      >
                        <Settings className="w-4 h-4 mr-3" />
                        Settings
                      </button>
                      <div className="h-px bg-neutral-200 dark:bg-neutral-700 my-2"></div>
                      <button
                        onClick={() => {
                          setShowProfileDropdown(false);
                          logout();
                          navigate('/login');
                        }}
                        className="w-full flex items-center px-4 py-2 text-sm text-danger-600 hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
                      >
                        <LogOut className="w-4 h-4 mr-3" />
                        Logout
                      </button>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Mobile Menu Button */}
          <div className="flex items-center space-x-2 lg:hidden">
            {/* Mobile Search Button */}
            <button
              onClick={() => setShowSearchModal(true)}
              className="p-1.5 sm:p-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
            >
              <Search className="w-5 h-5 sm:w-6 sm:h-6 text-neutral-600 dark:text-neutral-300" />
            </button>

            {/* Mobile Menu Toggle */}
            <button
              className="p-1.5 sm:p-2 rounded-md hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="w-5 h-5 sm:w-6 sm:h-6 text-neutral-600 dark:text-neutral-300" />
              ) : (
                <Menu className="w-5 h-5 sm:w-6 sm:h-6 text-neutral-600 dark:text-neutral-300" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="lg:hidden fixed inset-x-0 top-14 sm:top-16 bg-white dark:bg-[#202020] border-t border-neutral-200 dark:border-[#232323] shadow-lg z-50 max-h-[calc(100vh-3.5rem)] sm:max-h-[calc(100vh-4rem)] overflow-y-auto"
            >
              <div className="p-3 sm:p-4 space-y-3 sm:space-y-4">
                {/* Mobile Score Cards */}
                <div className="hidden sm:grid grid-cols-2 gap-2">
                  {renderScoreCard(stats.cyberHygieneScore, "Cyber Hygiene Score")}
                  {renderScoreCard(stats.vendorRiskRating, "Assets Risk Rating")}
                  {renderScoreCard(stats.threatScore, "Threat Intelligence Score")}
                  {renderScoreCard(stats.attackSurfaceIndex, "Attack Surface Index")}
                </div>

                <div className="space-y-2">
                  {/* Add all sidebar navigation items here */}
                  <button
                    onClick={() => {
                      navigate('/');
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <Home className="w-4 h-4 mr-3" />
                    Dashboard
                  </button>
                  <button
                    onClick={() => {
                      navigate('/assessment');
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <Scan className="w-4 h-4 mr-3" />
                    Assessment Center
                  </button>
                  <button
                    onClick={() => {
                      navigate('/assets-inventory');
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <UserCircle className="w-4 h-4 mr-3" />
                    Assets Inventory
                  </button>
                  <button
                    onClick={() => {
                      navigate('/vulnerability-hub');
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <AlertOctagon className="w-4 h-4 mr-3" />
                    Vulnerability Hub
                  </button>
                  <button
                    onClick={() => {
                      navigate('/reports');
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <FileText className="w-4 h-4 mr-3" />
                    Reports
                  </button>
                  <button
                    onClick={() => {
                      navigate('/settings');
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <Settings className="w-4 h-4 mr-3" />
                    Settings
                  </button>
                  <button
                    onClick={() => {
                      navigate('/profile');
                      setIsMobileMenuOpen(false);
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <User className="w-4 h-4 mr-3" />
                    Profile
                  </button>
                  <button
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      logout();
                      navigate('/login');
                    }}
                    className="w-full flex items-center px-4 py-2 text-sm text-danger-600 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md"
                  >
                    <LogOut className="w-4 h-4 mr-3" />
                    Logout
                  </button>

                  {/* Dark/Light Mode Toggle */}
                  <button
                    onClick={toggleTheme}
                    className="w-full flex items-center px-4 py-2 text-sm text-neutral-700 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-md mt-2"
                  >
                    {theme === 'dark' ? (
                      <Sun className="w-4 h-4 mr-3" />
                    ) : (
                      <Moon className="w-4 h-4 mr-3" />
                    )}
                    {theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>

      <SearchModal
        isOpen={showSearchModal}
        onClose={() => setShowSearchModal(false)}
        onSearch={onSearch}
      />
    </>
  );
};

export default Navbar;