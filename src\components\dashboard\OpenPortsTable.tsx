import React, { useState, useEffect } from "react";
import { Info, ArrowRight } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useDashboard } from "../../context/DashboardContext";

interface Port {
  id: string;
  port: number;
  service: string;
  protocol: string;
  state: string;
  riskLevel: string;
  notes?: string;
}

interface OpenPortsTableProps {
  ports?: Port[];
  showAll?: boolean;
}

const OpenPortsTable: React.FC<OpenPortsTableProps> = React.memo(({
  ports: propPorts,
  showAll = false,
}) => {
  const navigate = useNavigate();
  const { ports: contextPorts } = useDashboard();
  const [ports, setPorts] = useState<Port[]>([]);

  useEffect(() => {
    if (propPorts && propPorts.length > 0) {
      setPorts(propPorts);
    } else if (contextPorts && contextPorts.length > 0) {
      setPorts(contextPorts);
    }
  }, [propPorts, contextPorts]);

  const displayedPorts = showAll ? ports : ports.slice(0, 7);

  return (
    <div
      className="bg-white dark:bg-[#202020] rounded-[10px] shadow-sm border border-neutral-200 dark:border-[#232323] p-6 flex flex-col h-full"
      style={{ minWidth: 0, minHeight: "540px" }}
    >
      {/* Header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-1 whitespace-nowrap mr-3">
          <h3 className="text-base font-semibold text-neutral-900 dark:text-white whitespace-nowrap">
            Open Ports & Services
          </h3>
          <div className="tooltip-container relative group">
            <Info className="w-4 h-4 text-neutral-400 dark:text-neutral-400 ml-1 cursor-help" />
            <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
              Network ports that are currently open and accessible from the internet. Open ports can be entry points for attackers if not properly secured.
            </div>
          </div>
        </div>
        {/* <button className="bg-transparent border border-neutral-400 dark:border-neutral-600 rounded-md px-2 py-1 text-xs text-neutral-700 dark:text-neutral-300 hover:border-neutral-500 dark:hover:border-neutral-500 transition-colors whitespace-nowrap">
          View Details
        </button> */}
      </div>

      {/* Table */}
      <div className="flex-1 overflow-x-auto">
        <div className="rounded-xl border border-neutral-200 dark:border-neutral-700 overflow-hidden text-center">
          <table className="w-full min-w-[240px] table-fixed">
            <colgroup>
              <col style={{ width: "50%" }} />
              <col style={{ width: "50%" }} />
            </colgroup>
            <thead>
              <tr>
                <th className="bg-neutral-100 dark:bg-black text-neutral-900 dark:text-white font-semibold py-2 px-4 text-left text-xs border-b border-neutral-200 dark:border-neutral-700 whitespace-nowrap text-center">
                  Port
                </th>
                <th className="bg-neutral-100 dark:bg-black text-neutral-900 dark:text-white font-semibold py-2 px-4 text-left text-xs border-b border-neutral-200 dark:border-neutral-700 whitespace-nowrap text-center">
                  Service
                </th>
              </tr>
            </thead>
            <tbody>
              {displayedPorts.length > 0 ? (
                displayedPorts.map((port) => (
                  <tr
                    key={port.id}
                    className="border-b border-neutral-200 dark:border-neutral-700 last:border-b-0"
                  >
                    <td className="py-3 px-4 text-neutral-900 dark:text-white font-mono text-sm whitespace-nowrap overflow-hidden text-ellipsis">
                      {port.port}
                    </td>
                    <td className="py-3 px-4 text-neutral-900 dark:text-white text-sm whitespace-nowrap overflow-hidden text-ellipsis">
                      {port.service}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan={2}
                    className="text-center py-4 text-neutral-500 dark:text-neutral-400 bg-white dark:bg-neutral-900 text-xs"
                  >
                    No open ports found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Show All */}
      {!showAll && ports.length > 7 && (
        <div className="mt-4 flex justify-center">
          <button
            onClick={() => navigate("/ports")}
            className="flex items-center text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 font-medium text-sm"
          >
            Show All
            <ArrowRight className="w-4 h-4 ml-1" />
          </button>
        </div>
      )}
    </div>
  );
});

export default OpenPortsTable;