import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>A<PERSON>s, Tooltip, ResponsiveContainer, LabelList, Cell, TooltipProps } from 'recharts';
import assetsInventory from "../../pages/assetsinventoryjson.json"; // import local JSON

interface BarData {
  severity: string;
  count: number;
}

const CustomTooltip: React.FC<TooltipProps<number, string>> = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const entry = payload[0].payload as BarData;
    // Get vulnerability counts from local JSON
    const resultCount = assetsInventory?.report?.report?.result_count;
    const high = Number(resultCount?.high?.full ?? 0);
    const medium = Number(resultCount?.medium?.full ?? 0);
    const low = Number(resultCount?.low?.full ?? 0);
    const totalVulns = high + medium + low;

    return (
      <div className="bg-white dark:bg-neutral-900 p-2 rounded shadow text-xs border border-neutral-200 dark:border-neutral-700">
        <div><b>Total:</b> {totalVulns}</div>
        <div><b>{entry.severity}:</b> {entry.count}</div>
      </div>
    );
  }
  return null;
};

const AssetsRiskRating: React.FC = () => {
  // Get vulnerability counts from local JSON
  const resultCount = assetsInventory?.report?.report?.result_count;
  const high = Number(resultCount?.high?.full ?? 0);
  const medium = Number(resultCount?.medium?.full ?? 0);
  const low = Number(resultCount?.low?.full ?? 0);
  // const total = high + medium + low;

  const barData = [
    // { severity: "Total", count: total },
    { severity: "High", count: high },
    { severity: "Medium", count: medium },
    { severity: "Low", count: low },
  ];

  return (
    <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-4 " style={{ height: "auto" }}>
      <div className="flex flex-wrap items-center gap-2 sm:gap-1 mb-2 sm:mb-4">
        <h3 className="text-base sm:text-lg font-semibold text-neutral-900 dark:text-white whitespace-nowrap">
          Asset Risk by Severity
        </h3>
        <div className="tooltip-container relative group">
          <div className="w-4 h-4 rounded-full border border-neutral-400 dark:border-neutral-400 flex items-center justify-center cursor-help">
            <span className="text-xs text-neutral-400">i</span>
          </div>
          <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
            Shows the total number of vulnerabilities by severity (High, Medium, Low).
          </div>
        </div>
      </div>
      <div className="w-full" style={{ minHeight: 120, height: "40vw", maxHeight: 180 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={barData} margin={{ top: 0, right: 0, left: -20, bottom: -10 }}>
            <XAxis dataKey="severity" stroke="#888" fontSize={12} />
            <YAxis allowDecimals={false} stroke="#888" fontSize={12} />
            <Tooltip content={<CustomTooltip />} />
            <Bar dataKey="count" radius={[8, 8, 0, 0]} barSize={20}>
              <LabelList dataKey="count" position="top" fontSize={12} />
              {barData.map((entry, idx) => (
                <Cell
                  key={`cell-${idx}`}
                  fill={
                    entry.severity === "High"
                      ? "#ef4444"
                      : entry.severity === "Medium"
                      ? "#f59e42"
                      : entry.severity === "Low"
                      ? "#22c55e"
                      : "#6366f1"
                  }
                />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default AssetsRiskRating;

