
import React, { useState } from 'react';
import { Search, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSearch: (query: string) => void;
}

const SearchModal: React.FC<SearchModalProps> = ({ isOpen, onClose, onSearch }) => {
  const [query, setQuery] = useState('');
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6 md:p-8"
          >
            <div className="w-full max-w-2xl bg-white dark:bg-neutral-900 rounded-lg shadow-xl">
              <div className="p-4 sm:p-6">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg sm:text-xl font-semibold text-neutral-800 dark:text-neutral-100">AI Security Assistant</h2>
                  <button
                    onClick={onClose}
                    className="text-neutral-500 hover:text-neutral-700 dark:text-neutral-300 dark:hover:text-neutral-100 transition-colors p-1 hover:bg-neutral-100 dark:hover:bg-neutral-800 rounded-full"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                
                <form onSubmit={(e) => {
                  e.preventDefault();
                  if (query.trim()) {
                    onSearch(query.trim());
                    onClose();
                    setQuery('');
                  }
                }}>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Search className="w-5 h-5 text-neutral-400 dark:text-neutral-300" />
                    </div>
                    <input
                      type="text"
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      className="w-full pl-10 pr-4 py-3 text-base sm:text-lg bg-neutral-50 dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent text-neutral-900 dark:text-neutral-100"
                      placeholder="Ask about vulnerabilities, endpoints..."
                      autoFocus
                    />
                  </div>
                </form>
                
                <div className="mt-6">
                  <h3 className="text-sm font-medium text-neutral-600 dark:text-neutral-300 mb-3">Try asking about:</h3>
                  <div className="flex flex-wrap gap-2">
                    <button
                      type="button"
                      onClick={() => {
                        const searchQuery = "High severity vulnerabilities";
                        setQuery(searchQuery);
                        onSearch(searchQuery);
                        onClose();
                      }}
                      className="px-3 py-1.5 text-sm bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                    >
                      High severity vulnerabilities
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const searchQuery = "Open ports";
                        setQuery(searchQuery);
                        onSearch(searchQuery);
                        onClose();
                      }}
                      className="px-3 py-1.5 text-sm bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                    >
                      Open ports
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const searchQuery = "Exposed endpoints";
                        setQuery(searchQuery);
                        onSearch(searchQuery);
                        onClose();
                      }}
                      className="px-3 py-1.5 text-sm bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                    >
                      Exposed endpoints
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const searchQuery = "Latest scan results";
                        setQuery(searchQuery);
                        onSearch(searchQuery);
                        onClose();
                      }}
                      className="px-3 py-1.5 text-sm bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200 rounded-full hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors"
                    >
                      Latest scan results
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default SearchModal;