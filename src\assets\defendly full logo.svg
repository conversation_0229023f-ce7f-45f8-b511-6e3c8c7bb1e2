<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Creator: CorelDRAW X7 -->
<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="1043mm" height="199.906mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"
viewBox="0 0 69850 13388"
 xmlns:xlink="http://www.w3.org/1999/xlink">
 <defs>
  <style type="text/css">
   <![CDATA[
    .fil3 {fill:#5AC15B}
    .fil2 {fill:#DC332A}
    .fil4 {fill:#FED806}
    .fil0 {fill:url(#id0)}
    .fil1 {fill:url(#id1);fill-rule:nonzero}
   ]]>
  </style>
  <linearGradient id="id0" gradientUnits="userSpaceOnUse" x1="11741" y1="5595.76" x2="-49.3706" y2="7792.01">
   <stop offset="0" style="stop-opacity:1; stop-color:#00457F"/>
   <stop offset="1" style="stop-opacity:1; stop-color:#1F1B20"/>
  </linearGradient>
  <linearGradient id="id1" gradientUnits="userSpaceOnUse" x1="68267.7" y1="4829.76" x2="15786.4" y2="8557.94">
   <stop offset="0" style="stop-opacity:1; stop-color:#204691"/>
   <stop offset="1" style="stop-opacity:1; stop-color:black"/>
  </linearGradient>
 </defs>
 <g id="Layer_x0020_1">
  <metadata id="CorelCorpID_0Corel-Layer"/>
  <path class="fil0" d="M1064 2784c784,-206 1632,-569 2410,-821 390,-127 2153,-818 2384,-825 205,-6 4206,1455 4774,1645 -52,1559 -50,3505 -552,5006 -101,300 -440,1082 -597,1199l20 -3359 -1210 2 0 4758c-51,133 -669,687 -855,787l-4 -2892 -1116 -4 -56 3691c-403,303 -466,262 -889,-2l-22 -4290 -1121 -1 -31 3497 -1476 -1459c-404,-503 -808,-1085 -1064,-1818 -522,-1494 -613,-3473 -595,-5114zm4724 -2784l-4624 1653c-1342,465 -1167,182 -1144,2000 21,1742 214,3747 967,5301 231,477 736,1269 1092,1654 523,565 1212,1212 1863,1659 268,185 1660,1184 1991,1118 260,-52 1713,-1042 1927,-1213l855 -710c321,-278 522,-441 795,-776 584,-714 788,-908 1265,-1871 720,-1452 902,-3719 906,-5358 5,-1737 195,-1330 -1281,-1860 -454,-163 -4561,-1610 -4612,-1597z"/>
  <path class="fil1" d="M14204 3427l0 6537 3269 0c902,0 1718,-365 2308,-960 65,-67 128,-135 187,-205 484,-567 772,-1305 772,-2105 0,-1806 -1464,-3267 -3267,-3267l-3269 0zm1164 1165l2105 0c1157,0 2102,945 2102,2102 0,1161 -945,2105 -2102,2105l-2105 0 0 -4207zm8072 -3l4773 0 0 -1165 -4773 0c-975,0 -1763,793 -1763,1767l0 3005c0,975 788,1768 1763,1768l4773 0 0 -1165 -4773 0c-330,0 -598,-273 -598,-603l0 -1234 4207 0 0 -1165 -4207 0 0 -606c0,-330 268,-602 598,-602l0 0zm7473 -1165c-974,0 -1762,793 -1762,1767l0 4769 1161 0 0 -2998 4210 0 0 -1165 -4207 0 0 -606c0,-330 268,-602 598,-602l4774 0 0 -1165 -4774 0zm7474 1165l4774 0 0 -1165 -4774 0c-975,0 -1763,793 -1763,1767l0 3005c0,975 788,1768 1763,1768l4774 0 0 -1165 -4774 0c-330,0 -598,-273 -598,-603l0 -1234 4207 0 0 -1165 -4207 0 0 -606c0,-330 268,-602 598,-602l0 0zm11082 -1165l0 4164 -4202 -3239 -1169 -901 0 6516 1169 0 0 -4140 4202 3240 1168 900 0 -6540 -1168 0zm5372 2l-3270 0 0 6536 3270 0c901,0 1717,-364 2307,-959 66,-66 128,-136 187,-206 485,-566 773,-1304 773,-2105 0,-1806 -1464,-3266 -3267,-3266l0 0zm0 5371l-2105 0 0 -4205 2105 0c1157,0 2102,943 2102,2100 0,1162 -945,2105 -2102,2105l0 0zm6169 -1c-416,-55 -746,-385 -801,-801l0 -4571 -1164 0 0 4455c0,330 78,640 218,917 198,414 531,749 946,951 276,136 587,213 918,213l3313 0 0 -1164 -3430 0zm7372 -5384l-1798 2331 -1799 -2331 -1471 0 2532 3282 4 5 156 197 0 3064 1168 0 0 -3084 140 -177 5 -5 2531 -3278 0 -4 -1468 0zm-5068 6564l16 -20 -16 0 0 20zm6536 -5l0 -15 -12 0 12 15z"/>
  <g id="_2144397220608">
   <path class="fil2" d="M6446 6332c12,-24 28,-47 49,-68l1887 -1979c-49,-92 -76,-197 -76,-308 0,-372 309,-673 690,-673 381,0 690,301 690,673 0,372 -309,674 -690,674 -66,0 -129,-9 -190,-26l-1910 2003c-3,4 -7,7 -10,11l-440 -307z"/>
   <path class="fil3" d="M5479 5917l527 415c113,-77 250,-123 398,-123 360,0 690,310 690,674 0,372 -309,674 -690,674 -381,0 -690,-302 -690,-674 0,-29 2,-58 5,-87l-531 -416 291 -463z"/>
   <path class="fil4" d="M4790 5211c381,0 690,302 690,674 0,5 0,11 0,16 0,6 -1,11 -1,16 -66,356 -309,641 -689,641 -81,0 -159,-13 -231,-38l-1657 1515c-301,275 -628,-161 -376,-392l1631 -1491c-37,-82 -57,-172 -57,-267 0,-372 309,-674 690,-674z"/>
  </g>
 </g>
</svg>
