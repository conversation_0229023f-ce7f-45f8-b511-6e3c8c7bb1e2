import axios from 'axios';
import TokenManager from '../utils/tokenManager';

const API_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001';

// Create an axios instance with auth headers
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = TokenManager.getToken();
    if (token && !TokenManager.isTokenExpired()) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add response interceptor to handle token expiration
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      TokenManager.clearToken();
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

const scanApi = {
  // Get the latest scan data
  getLatestScan: async () => {
    try {
      const response = await api.get('/api/scans/latest');
      return response.data;
    } catch (error) {
      console.error('Error fetching latest scan');
      throw error;
    }
  },

  // Get a scan by ID
  getScanById: async (scanId: string) => {
    try {
      const response = await api.get(`/api/scans/${scanId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching scan by ID');
      throw error;
    }
  },

  // Initiate a new scan
  initiateScan: async (url: string) => {
    try {
      const response = await api.post('/api/scans/initiate', { url });
      return response.data;
    } catch (error) {
      console.error('Error initiating scan:', error);
      throw error;
    }
  },

  // Poll for scan status until it's completed
  pollScanStatus: async (scanId: string, onStatusUpdate: (status: string) => void) => {
    let intervalId: number | undefined = undefined;

    const checkStatus = async () => {
      try {
        const scanData = await scanApi.getScanById(scanId);
        const status = scanData.status || 'pending';
        
        onStatusUpdate(status);
        
        if (status === 'completed' || status === 'failed') {
          if (intervalId) {
            clearInterval(intervalId);
          }
        }
      } catch (error) {
        console.error(`Error polling scan ${scanId}:`, error);
        // Don't clear interval on error - keep trying
      }
    };

    // Start polling
    await checkStatus(); // Check immediately first
    intervalId = window.setInterval(checkStatus, 5000); // Then every 5 seconds

    // Return a function to stop polling
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }
};

export default scanApi;