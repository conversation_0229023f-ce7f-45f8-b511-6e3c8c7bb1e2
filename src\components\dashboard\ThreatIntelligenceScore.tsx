
import React from 'react';
import { useDashboard } from '../../context/DashboardContext';

// Gauge component for semicircle display matching Figma exactly
const ThreatGaugeChart: React.FC<{
  score: number;
  maxScore: number;
  color: string;
}> = ({ score, maxScore, color }) => {
  const segments: JSX.Element[] = [];
  const totalSegments = 24; // More segments for smoother look like Figma
  const activeSegments = Math.round((score / maxScore) * totalSegments);

  // Create semicircle segments (180 degrees from left to right)
  for (let i = 0; i < totalSegments; i++) {
    const isActive = i < activeSegments;

    // Calculate position for each segment
    const centerX = 90;
    const centerY = 90;
    const radius = 60;
    const segmentLength = 15;

    // Upward semicircle: start from 180 degrees (left) to 0 degrees (right) on TOP half
    const angle = 180 + (i / (totalSegments - 1)) * 180; // 180 to 360 degrees (top semicircle)
    const angleRad = (angle * Math.PI) / 180;
    const x1 = centerX + (radius - segmentLength/2) * Math.cos(angleRad);
    const y1 = centerY + (radius - segmentLength/2) * Math.sin(angleRad);
    const x2 = centerX + (radius + segmentLength/2) * Math.cos(angleRad);
    const y2 = centerY + (radius + segmentLength/2) * Math.sin(angleRad);

    segments.push(
      <line
        key={i}
        x1={x1}
        y1={y1}
        x2={x2}
        y2={y2}
        stroke={isActive ? color : '#374151'}
        strokeWidth="6"
        strokeLinecap="round"
      />
    );
  }

  return (
    <div className="relative flex flex-col items-center">
      <svg width="180" height="100" className="mb-2">
        {segments}
      </svg>

      {/* Score display */}
      <div className="absolute top-12 left-1/2 transform -translate-x-1/2 flex flex-col items-center">
        <div
          className="text-3xl font-bold"
          style={{ color }}
        >
          {score}
        </div>
        <div className="text-xs text-neutral-400 -mt-1">
          Out of {maxScore}
        </div>
      </div>
    </div>
  );
};

const ThreatIntelligenceScore: React.FC = () => {
  const { stats, loadingData, rawApiData } = useDashboard();
  const score = stats?.threatScore ?? 0;

  // Get additional data from raw API response
  const threatData = rawApiData?.threat_intelligence;
  const threatScore = threatData?.threat_intelligence_score?.score ?? score;
  const assessment = threatData?.threat_intelligence_score?.assessment || "No assessment available";
  const confidence = threatData?.threat_intelligence_score?.confidence || "Unknown";
  const riskAssessment = threatData?.reputation_data?.risk_assessment || "Unknown";

  // Calculate risk level and message based on score with API data
  const getRiskLevel = (score: number) => {
    // Use API assessment if available
    if (assessment && assessment !== "No assessment available") {
      const color = assessment.toLowerCase().includes("critical") ? "#EF4444" :
                   assessment.toLowerCase().includes("high") ? "#F97316" :
                   assessment.toLowerCase().includes("moderate") ? "#FFA500" : "#22C55E";
      return { level: assessment, color };
    }

    // Fallback calculation
    if (score >= 80) return { level: "Low Risk - Well Protected", color: "#22C55E" };
    if (score >= 60) return { level: "Moderate Risk - Maintain Vigilance", color: "#FFA500" };
    if (score >= 40) return { level: "High Risk - Increase Security", color: "#F97316" };
    return { level: "Critical Risk - Immediate Action", color: "#EF4444" };
  };

  const riskInfo = getRiskLevel(score);

  if (loadingData) {
    return (
      <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-4 flex flex-col items-center justify-center" style={{ height: 'auto' }}>
        <span className="text-lg animate-pulse text-neutral-600 dark:text-neutral-400">Loading...</span>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-4 flex flex-col" style={{ height: 'auto' }}>
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-1 flex-1 min-w-0 mr-3">
          <h3 className="text-base font-semibold text-neutral-900 dark:text-white whitespace-nowrap">
            Threat Intelligence Score
          </h3>
          <div className="tooltip-container relative group">
            <div className="w-4 h-4 rounded-full border border-neutral-400 dark:border-neutral-400 flex items-center justify-center flex-shrink-0 cursor-help">
              <span className="text-xs text-neutral-400">i</span>
            </div>
            <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
              Intelligence-based threat assessment using reputation data, malware analysis, and threat indicators. Helps identify potential security risks.
            </div>
          </div>
        </div>
        {/* <button className="border border-neutral-400 dark:border-neutral-600 rounded-md px-2 py-1 text-xs text-neutral-700 dark:text-neutral-300 hover:border-neutral-500 transition-colors whitespace-nowrap">
          View Details
        </button> */}
      </div>

      {/* Gauge Chart */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <ThreatGaugeChart score={threatScore} maxScore={100} color={riskInfo.color} />

        {/* Bottom text */}
        <div className="mt-2 text-center">
          <div className="text-sm text-neutral-900 dark:text-white">
            {riskInfo.level}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThreatIntelligenceScore;