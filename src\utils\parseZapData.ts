import { Vulnerability, VulnerableEndpoint } from '../types/types';

interface ZapAlert {
  pluginid: string;
  alertRef: string;
  name: string;
  riskcode: string;
  confidence: string;
  description: string;
  count: number;
  solution: string;
  otherinfo: string;
  reference: string;
  cweid: string;
  wascid: string;
  sourceid: string;
  instances: {
    uri: string;
    method: string;
    param: string;
    attack: string;
    evidence: string;
  }[];
}

interface ZapScan {
  site: {
    '@name': string;
    '@host': string;
    '@port': string;
    '@ssl': string;
  };
  alerts: ZapAlert[];
}

export const parseZapData = (zapData: ZapScan) => {
  const vulnerabilities: Vulnerability[] = [];
  const endpoints: VulnerableEndpoint[] = [];
  
  // Process each alert in the ZAP data
  zapData.alerts.forEach(alert => {
    // Map ZAP risk code to severity
    let severity: 'high' | 'medium' | 'low' | 'info';
    switch (alert.riskcode) {
      case '3':
        severity = 'high';
        break;
      case '2':
        severity = 'medium';
        break;
      case '1':
        severity = 'low';
        break;
      default:
        severity = 'info';
    }

    // Map CWE to compliance frameworks
    const complianceMapping = [];
    if (alert.cweid) {
      complianceMapping.push(`CWE-${alert.cweid}`);
    }
    
    // Determine attack vector based on the alert type
    let attackVector = 'Other';
    
    // This is a simplified mapping - a real implementation would be more comprehensive
    if (alert.name.includes('SQL Injection')) {
      attackVector = 'Injection';
    } else if (alert.name.includes('XSS') || alert.name.includes('Cross Site Scripting')) {
      attackVector = 'XSS';
    } else if (alert.name.includes('CSRF')) {
      attackVector = 'CSRF';
    } else if (alert.name.includes('Path Traversal')) {
      attackVector = 'Path Traversal';
    } else if (alert.name.includes('Information Disclosure') || alert.name.includes('Information Leakage')) {
      attackVector = 'Information Disclosure';
    } else if (alert.name.includes('Authentication') || alert.name.includes('Authorization')) {
      attackVector = 'Authentication';
    }
    
    // Process each instance of the vulnerability
    alert.instances.forEach((instance, index) => {
      // Create vulnerability entry
      const vulnerability: Vulnerability = {
        id: `zap-${alert.pluginid}-${index}`,
        name: alert.name,
        severity,
        endpoint: instance.uri,
        attackVector,
        description: alert.description,
        remediation: alert.solution,
        complianceMapping
      };
      
      vulnerabilities.push(vulnerability);
      
      // Create vulnerable endpoint entry
      const endpoint: VulnerableEndpoint = {
        id: `endpoint-${alert.pluginid}-${index}`,
        path: instance.uri,
        method: instance.method,
        vulnerabilityType: alert.name,
        severity
      };
      
      endpoints.push(endpoint);
    });
  });
  
  return {
    vulnerabilities,
    endpoints
  };
};