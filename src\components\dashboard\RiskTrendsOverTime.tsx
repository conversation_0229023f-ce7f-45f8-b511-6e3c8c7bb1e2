import React, { useState, useMemo } from "react";
import { useDashboard } from "../../context/DashboardContext";
import {
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Responsive<PERSON>ontainer,
  <PERSON>
} from "recharts";

// Color map for each severity
const severityColors = {
  critical: "#EF4444", // Red
  high: "#F97316",     // Orange
  medium: "#EAB308",   // Yellow
  low: "#22C55E",      // Green
  info: "#3B82F6",     // Blue
};

// The 3 filter buttons
const FILTERS = ["Monthly", "Weekly", "Today"];

// Helper to build chart data
interface TimeLabelsMap {
  [key: string]: string[];
}

type FilterType = "Monthly" | "Weekly" | "Today";

function getCurrentWeekOfMonth(date: Date): number {
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  const dayOfWeek = firstDay.getDay(); // 0 (Sun) - 6 (Sat)
  const adjustedDate = date.getDate() + dayOfWeek;
  return Math.ceil(adjustedDate / 7);
}

const getTimeLabels = (filter: FilterType): string[] => {
  const now = new Date();
  if (filter === "Monthly") {
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const idx = now.getMonth();
    // Center month is current; show 2 before/after
    return [
      monthNames[(idx + 10) % 12],
      monthNames[(idx + 11) % 12],
      monthNames[idx],
      monthNames[(idx + 1) % 12],
      monthNames[(idx + 2) % 12],
    ];
  }
  if (filter === "Weekly") return ["W1", "W2", "W3", "W4"];
  return ["6AM", "12PM", "6PM", "12AM"];
};

function getCurrentTimeSlotIndex(date: Date): number {
  const hour = date.getHours();
  if (hour >= 0 && hour < 6) return 3;      // 12AM-6AM
  if (hour >= 6 && hour < 12) return 0;     // 6AM-12PM
  if (hour >= 12 && hour < 18) return 1;    // 12PM-6PM
  return 2;                                 // 6PM-12AM
}

export default function RiskTrendsOverTimeChart() {
  const { vulnerabilities } = useDashboard();
  const [filter, setFilter] = useState<FilterType>("Monthly");

  // Tally current API data for severity
  const severityCounts = useMemo(() => {
    const counts = vulnerabilities.reduce((acc: Record<string, number>, v) => {
      const sev = (v.severity || "info").toLowerCase();
      acc[sev] = (acc[sev] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    return {
      critical: counts.critical || 0,
      high: counts.high || 0,
      medium: counts.medium || 0,
      low: counts.low || 0,
      info: counts.info || 0,
    };
  }, [vulnerabilities]);

  // Prepare chart data for selected filter
const chartData = useMemo(() => {
  const labels = getTimeLabels(filter);
  let currentIdx = 0;
  if (filter === "Monthly") {
    currentIdx = 2;
  } else if (filter === "Weekly") {
    currentIdx = getCurrentWeekOfMonth(new Date()) - 1; // 0-based index
  } else if (filter === "Today") {
    currentIdx = getCurrentTimeSlotIndex(new Date());
  } else {
    currentIdx = labels.length - 1;
  }

  return labels.map((label, idx) => {
    if (filter === "Monthly") {
      if (idx < currentIdx) {
        // Before current month: zero (or you could use historic if you ever have)
        return {
          period: label,
          critical: 0,
          high: 0,
          medium: 0,
          low: 0,
          info: 0,
        };
      }
      if (idx === currentIdx) {
        // Current month: API data
        return {
          period: label,
          critical: severityCounts.critical,
          high: severityCounts.high,
          medium: severityCounts.medium,
          low: severityCounts.low,
          info: severityCounts.info,
        };
      }
      // After current month: null for all values so line stops
      return {
        period: label,
        critical: null,
        high: null,
        medium: null,
        low: null,
        info: null,
      };
    }
    // For Weekly/Today, only last index gets data, others zero
    return idx === currentIdx
      ? {
          period: label,
          critical: severityCounts.critical,
          high: severityCounts.high,
          medium: severityCounts.medium,
          low: severityCounts.low,
          info: severityCounts.info,
        }
      : {
          period: label,
          critical: 0,
          high: 0,
          medium: 0,
          low: 0,
          info: 0,
        };
  });
}, [filter, severityCounts]);


  // Find Y axis max for nice scaling
  const maxY = Math.max(
    10,
    ...chartData.map(d =>
      Math.max(
        d.critical ?? 0,
        d.high ?? 0,
        d.medium ?? 0,
        d.low ?? 0,
        d.info ?? 0
      )
    )
  );

  return (
    <div
      className="relative rounded-[10px] w-full h-full bg-white dark:bg-[#202020] border border-neutral-200 dark:border-[#232323] p-4 sm:p-6"
      style={{ minHeight: 320, fontFamily: "Inter, sans-serif" }}
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-2 sm:mb-6 gap-2">
        <div>
          <div className="flex items-center gap-2">
            <h3 className="text-base sm:text-lg text-neutral-900 dark:text-white font-semibold mb-1">
              Risk Trends Over Time
            </h3>
            <div className="tooltip-container relative group">
              <div className="w-4 h-4 rounded-full border border-neutral-400 dark:border-neutral-400 flex items-center justify-center cursor-help">
                <span className="text-xs text-neutral-400">i</span>
              </div>
              <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
                Shows the trend of vulnerabilities by severity over time.
              </div>
            </div>
          </div>
          <div className="flex flex-wrap gap-x-4 gap-y-1 mt-1">
            {Object.entries(severityCounts).map(([key, count]) => (
              <span key={key} className="flex items-center gap-1">
                <span
                  style={{
                    background: severityColors[key as keyof typeof severityColors],
                    borderRadius: "50%",
                    width: 10,
                    height: 10,
                    display: "inline-block"
                  }}
                ></span>
                <span
                  className="font-semibold dark:text-white"
                  style={{
                    fontSize: 13,
                    marginRight: 2,
                    fontFamily: "inherit",
                  }}
                >
                  {count}
                </span>
                <span
                  className="text-xs text-neutral-600 dark:text-[#9CA3AF]"
                  style={{ fontFamily: "inherit" }}
                >
                  {key.charAt(0).toUpperCase() + key.slice(1)}
                </span>
              </span>
            ))}
          </div>
        </div>
        {/* Filters */}
        <div className="flex flex-wrap gap-2 mt-2 sm:mt-0">
          {FILTERS.map(f => (
            <button
              key={f}
              className={`px-3 py-1 rounded-md text-xs font-semibold ${
                filter === f
                  ? "bg-[#00457f] text-white"
                  : "bg-neutral-200 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200"
              }`}
              style={{ fontFamily: "inherit", minWidth: 70 }}
              onClick={() => setFilter(f as FilterType)}
            >
              {f}
            </button>
          ))}
        </div>
      </div>
      {/* Chart */}
      <div className="w-full" style={{ minHeight: 180, height: "40vw", maxHeight: 270 }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <CartesianGrid
  stroke={
    document.documentElement.classList.contains("dark")
      ? "#666666"
      : "#d1d5db" // Tailwind's border-neutral-300
  }
  strokeDasharray="3 3"
/>
            <XAxis dataKey="period" stroke="#bdbdbd" fontSize={13} tickLine={false} axisLine={false} />
            <YAxis
              stroke="#bdbdbd"
              fontSize={13}
              tickLine={false}
              axisLine={false}
              domain={[0, maxY + 2]}
              allowDecimals={false}
            />
            <Tooltip
              contentStyle={{
                background: document.documentElement.classList.contains("dark") ? "#000000" : "#f5f5f5",
                borderRadius: 8,
                border: "none",
                color: document.documentElement.classList.contains("dark") ? "#fff" : "#222",
              }}
              labelStyle={{ color: document.documentElement.classList.contains("dark") ? "#fff" : "#000" }}
              cursor={{ stroke: "#2a2a2a", strokeWidth: 2 }}
            />
            {/* <Legend
              wrapperStyle={{ paddingTop: 8 }}
              iconType="circle"
              layout="horizontal"
              align="center"
              verticalAlign="top"
            /> */}
            {/* Lines for each severity */}
            <Line
              type="monotone"
              dataKey="critical"
              name="Critical"
              stroke={severityColors.critical}
              strokeWidth={2.5}
              dot={{ r: 4 }}
              activeDot={{ r: 7 }}
              connectNulls
            />
            <Line
              type="monotone"
              dataKey="high"
              name="High"
              stroke={severityColors.high}
              strokeWidth={2.5}
              dot={{ r: 4 }}
              activeDot={{ r: 7 }}
              connectNulls
            />
            <Line
              type="monotone"
              dataKey="medium"
              name="Medium"
              stroke={severityColors.medium}
              strokeWidth={2.5}
              dot={{ r: 4 }}
              activeDot={{ r: 7 }}
              connectNulls
            />
            <Line
              type="monotone"
              dataKey="low"
              name="Low"
              stroke={severityColors.low}
              strokeWidth={2.5}
              dot={{ r: 4 }}
              activeDot={{ r: 7 }}
              connectNulls
            />
            <Line
              type="monotone"
              dataKey="info"
              name="Informational"
              stroke={severityColors.info}
              strokeWidth={2.5}
              dot={{ r: 4 }}
              activeDot={{ r: 7 }}
              connectNulls
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
