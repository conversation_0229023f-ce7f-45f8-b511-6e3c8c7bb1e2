import React from "react";
import { useDashboard } from "../context/DashboardContext";
import CyberHygieneScore from "../components/dashboard/CyberHygieneScore";
import AssetsRiskRating from "../components/dashboard/VendorRiskRating";
import ComplianceMapping from "../components/dashboard/ComplianceMapping";
import OpenPortsTable from "../components/dashboard/OpenPortsTable";
import VulnerableEndpoints from "../components/dashboard/VulnerableEndpoints";
import ThreatIntelligenceScore from "../components/dashboard/ThreatIntelligenceScore";
import AttackSurfaceIndex from "../components/dashboard/AttackSurfaceIndex";
import VulnerabilitySummaryCard from "../components/dashboard/VulnerabilitySummaryCard";
import RiskTrendsOverTime from "../components/dashboard/RiskTrendsOverTime";
import SecurityHeaders from "../components/dashboard/SecurityHeaders";
import VulnerabilitiesTable from "../components/dashboard/VulnerabilitiesTable";
import CybersecurityNews from "../components/dashboard/CybersecurityNews";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import html2pdf from "html2pdf.js";

const Dashboard: React.FC = () => {
  const location = useLocation();
  
  const showSubPage = location.pathname !== "/";
  const { vulnerabilities, loadingData } = useDashboard();
  const dashboardRef = React.useRef<HTMLDivElement>(null);

const handleDownloadPDF = () => {
  if (dashboardRef.current) {
    const dashboardElement = dashboardRef.current;
    const rect = dashboardElement.getBoundingClientRect();
    const widthPx = Math.round(rect.width);
    const heightPx = Math.round(rect.height);

    // Convert px to pt (1px = 0.75pt)
    const widthPt = widthPx * 0.75;
    const heightPt = heightPx * 0.75;

    html2pdf()
      .set({
        margin: 0,
        filename: "dashboard.pdf",
        image: { type: "jpeg", quality: 0.98 },
        html2canvas: { scale: 2, useCORS: true, scrollY: 0 },
        jsPDF: { unit: "pt", format: [widthPt, heightPt], orientation: widthPx > heightPx ? "landscape" : "portrait" },
        pagebreak: { mode: [] }, // No page breaks, single page
      })
      .from(dashboardElement)
      .save();
  }
}; // <--- THIS closes your function!

  if (showSubPage) {
    return <Outlet />;
  }

  if (loadingData) {
    return (
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6 flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="w-12 h-12 sm:w-16 sm:h-16 lg:w-20 lg:h-20 border-4 border-neutral-200 dark:[#00457f] border-t-primary-500 rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-lg sm:text-xl lg:text-2xl font-semibold mb-2 dark:text-neutral-100">
            Loading Dashboard
          </h2>
          <p className="text-sm sm:text-base text-neutral-600 dark:text-neutral-300">
            Please wait while we load your security data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div ref={dashboardRef} className="max-w-[1920px] mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-6">
      {/* Header Section */}
      <div className="mb-2 sm:mb-4 lg:mb-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start space-y-4 sm:space-y-0">
          <div className="flex-1">
            <h1 className="text-xl sm:text-2xl lg:text-3xl xl:text-4xl font-bold text-neutral-800 dark:text-white mb-2 sm:mb-3">
              Cyber Risk Posture
            </h1>
            <p className="text-neutral-600 dark:text-neutral-300 text-xs sm:text-sm lg:text-base ">
              Executive-level insights into vulnerabilities, external threats,
              and security performance across your organization.
            </p>
          </div>

          {/* Controls Row: Download + Dropdowns aligned in one row */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 mt-1">
            <button
              onClick={handleDownloadPDF}
              className="flex items-center space-x-1 xl:space-x-2 px-2 xl:px-4 py-1.5 xl:py-2 bg-[#00457F] text-white rounded-md hover:bg-[#00345c] transition-colors"
            >
              <span className="text-xs xl:text-sm font-medium">Download</span>
            </button>
            <select
              className="bg-white dark:bg-[#202020] border border-neutral-400 dark:border-[#666666] rounded-md px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm text-neutral-700 dark:text-neutral-200 focus:outline-none focus:ring-1 focus:ring-neutral-400 hover:border-neutral-500 dark:hover:border-[#888888] transition-colors"
              style={{ colorScheme: 'dark' }}
            >
              <option className="bg-white dark:bg-[#202020] text-neutral-700 dark:text-neutral-200">Last Month</option>
              <option className="bg-white dark:bg-[#202020] text-neutral-700 dark:text-neutral-200">Last Week</option>
              <option className="bg-white dark:bg-[#202020] text-neutral-700 dark:text-neutral-200">Last 3 Months</option>
            </select>
            <select
              className="bg-white dark:bg-[#202020] border border-neutral-400 dark:border-[#666666] rounded-md px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm text-neutral-700 dark:text-neutral-200 focus:outline-none focus:ring-1 focus:ring-neutral-400 hover:border-neutral-500 dark:hover:border-[#888888] transition-colors"
              style={{ colorScheme: 'dark' }}
            >
              <option className="bg-white dark:bg-[#202020] text-neutral-700 dark:text-neutral-200">Previous Scans</option>
              <option className="bg-white dark:bg-[#202020] text-neutral-700 dark:text-neutral-200">All Scans</option>
              <option className="bg-white dark:bg-[#202020] text-neutral-700 dark:text-neutral-200">Recent Scans</option>
            </select>
          </div>
        </div>
      </div>

      {/* Responsive Grid Layout */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-3 sm:gap-4 lg:gap-6 mb-4 max-w-[1800px] mx-auto mt-2 sm:mt-4">
        {/* Left Column */}
        <div className="flex flex-col gap-3 sm:gap-4 lg:gap-6 w-full">
          {/* Cyber Hygiene Score */}
          <div className="w-full">
            <CyberHygieneScore />
          </div>

          {/* Risk Trends Over Time */}
          <div className="w-full">
            <RiskTrendsOverTime />
          </div>

          {/* Asset Risk Rating & Threat Intelligence Score - Responsive */}
          <div className="w-full">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
              <AssetsRiskRating />
              <ThreatIntelligenceScore />
            </div>
          </div>

          {/* Top Vulnerabilities */}
          <div className="w-full">
            <VulnerabilitiesTable
              vulnerabilities={vulnerabilities.map((vul) => ({
                ...vul,
                severity: vul.severity as
                  | "critical"
                  | "high"
                  | "medium"
                  | "low"
                  | "info",
              }))}
            />
          </div>

          {/* Cybersecurity News */}
          <div className="w-full">
            <CybersecurityNews />
          </div>
        </div>

        {/* Right Column */}
        <div className="flex flex-col gap-3 sm:gap-4 lg:gap-6 w-full">
          {/* Vulnerability Summary */}
          <div className="w-full ">
            <VulnerabilitySummaryCard
              vulnerabilities={vulnerabilities.map((vul) => ({
                ...vul,
                severity: vul.severity as
                  | "critical"
                  | "high"
                  | "medium"
                  | "low"
                  | "info",
              }))}
            />
          </div>

          {/* Compliance Coverage - Responsive spacing */}
          <div className="w-full">
            <ComplianceMapping />
          </div>

          {/* Open Ports & Services + Security Headers - Responsive */}
          <div className="w-full">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
              <OpenPortsTable />
              <SecurityHeaders />
            </div>
          </div>

          {/* Attack Surface Index */}
          <div className="w-full">
            <AttackSurfaceIndex />
          </div>

          {/* Vulnerable Endpoints */}
          <div className="w-full">
            <VulnerableEndpoints />
          </div>
        </div>
      </div>
    </div>
  );
};


export default Dashboard;
