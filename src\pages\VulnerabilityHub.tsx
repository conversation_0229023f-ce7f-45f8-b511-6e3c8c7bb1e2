import React, { useState, useEffect, useCallback } from 'react';
import { useDashboard } from '../context/DashboardContext';
import VulnerabilityPieChart from '../components/vulnerability/VulnerabilityPieChart';
import VulnerabilityFilters from '../components/vulnerability/VulnerabilityFilters';
import VulnerabilityDataTable from '../components/vulnerability/VulnerabilityDataTable';
import { Tooltip } from 'recharts';

interface VulnerabilityRecord {
  id: string;
  vulnerability: string;
  target: string;
  type: string;
  severity: 'critical' | 'high' | 'medium' | 'low' | 'info';
  endpoint: string;
  attackVector: string;
  start: string;
  end: string;
  status: 'open' | 'in-progress' | 'resolved' | 'false-positive';
}

interface FilterState {
  organization: string;
  target: string;
  scanType: string;
  vulnerabilityType: string;
  severity: string;
  dateRange: string;
  status: string;
  attackVector: string;
}

const VulnerabilityHub: React.FC = () => {
  const { vulnerabilities } = useDashboard();
  const [selectedSeverity, setSelectedSeverity] = useState<string | null>(null);
  const [filters, setFilters] = useState<FilterState>({
    organization: '',
    target: '',
    scanType: '',
    vulnerabilityType: '',
    severity: '',
    dateRange: '',
    status: '',
    attackVector: '',
  });
  const [filteredVulnerabilities, setFilteredVulnerabilities] = useState<VulnerabilityRecord[]>([]);

  // Transform dashboard vulnerabilities to vulnerability hub format
  const transformedVulnerabilities: VulnerabilityRecord[] = React.useMemo(() => {
    return vulnerabilities.map((vuln, index) => {
      let sev = (vuln.severity?.toLowerCase() || 'info');
      if (!['critical', 'high', 'medium', 'low', 'info'].includes(sev)) sev = 'info';
      return {
        ...vuln, // Preserve all original API fields
        id: vuln.id || `vuln-${index + 1}`,
        vulnerability: vuln.name || 'Unknown Vulnerability',
        target: vuln.endpoint || 'Unknown Target',
        type: 'Defendly AI Scan',
        severity: sev as 'critical' | 'high' | 'medium' | 'low' | 'info',
        endpoint: vuln.endpoint || 'Unknown Endpoint',
        attackVector: vuln.attackVector || 'Unknown',
        start: new Date().toISOString().split('T')[0] + ' ' + new Date().toTimeString().split(' ')[0].slice(0, 5),
        end: '--------',
        status: 'open'
      };
    });
  }, [vulnerabilities]);

  // Calculate pie chart data
  const pieChartData = React.useMemo(() => {
    const severityCounts = transformedVulnerabilities.reduce((acc, vuln) => {
      acc[vuln.severity] = (acc[vuln.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const total = transformedVulnerabilities.length;
    const colors = {
      critical: '#EF4444',
      high: '#F97316',
      medium: '#EAB308',
      low: '#22C55E',
      info: '#3B82F6'
    };

    return Object.entries(severityCounts).map(([severity, count]) => ({
      name: severity,
      value: count,
      color: colors[severity as keyof typeof colors] || '#6B7280',
      percentage: total > 0 ? Math.round((count / total) * 100) : 0
    }));
  }, [transformedVulnerabilities]);

  // Apply filters function
  const applyFilters = useCallback(() => {
    let filtered = [...transformedVulnerabilities];

    // Apply severity filter from pie chart
    if (selectedSeverity) {
      filtered = filtered.filter(vuln => vuln.severity === selectedSeverity);
    }

    // Apply other filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        filtered = filtered.filter(vuln => {
          switch (key) {
            case 'severity':
              return vuln.severity === value;
            case 'status':
              return vuln.status === value;
            case 'vulnerabilityType':
              return vuln.vulnerability.toLowerCase().includes(value.toLowerCase());
            case 'target':
              return vuln.target.toLowerCase().includes(value.toLowerCase());
            case 'attackVector':
              return vuln.attackVector.toLowerCase().includes(value.toLowerCase());
            default:
              return true;
          }
        });
      }
    });

    setFilteredVulnerabilities(filtered);
  }, [transformedVulnerabilities, selectedSeverity, filters]);

  // Initialize with all data and apply filters when pie chart selection changes
  useEffect(() => {
    if (selectedSeverity) {
      applyFilters();
    } else {
      setFilteredVulnerabilities(transformedVulnerabilities);
    }
  }, [selectedSeverity, transformedVulnerabilities, applyFilters]);

  // Handler functions
  const handleFilterChange = (key: keyof FilterState, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleFilterSubmit = () => {
    applyFilters();
  };

  const handleFilterReset = () => {
    setFilters({
      organization: '',
      target: '',
      scanType: '',
      vulnerabilityType: '',
      severity: '',
      dateRange: '',
      status: '',
      attackVector: '',
    });
    setSelectedSeverity(null);
  };

  const handlePieChartClick = (severity: string) => {
    setSelectedSeverity(selectedSeverity === severity ? null : severity);
  };

  const handleVulnerabilityAction = (id: string, action: string) => {
    console.log(`Action ${action} on vulnerability ${id}`);
    // Handle vulnerability actions here
  };

  return (
    <div className="max-w-[1920px] mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-6">
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-neutral-800 dark:text-white mb-2">Vulnerabilities Hub</h1>
        <p className="text-neutral-600 dark:text-neutral-300 text-sm sm:text-base">View and manage all security scans across your organization</p>
      </div>

      {/* Main Tile: Contains Pie Chart and Filter Tile */}
      <div className="bg-white dark:bg-[#2A2A2A] rounded-lg p-3 sm:p-4 lg:p-6 border border-neutral-200 dark:border-neutral-700 mb-4 sm:mb-6" style={{ overflow: 'visible' }}>
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 sm:gap-6 h-full" style={{ overflow: 'visible' }}>
          {/* Pie Chart - Direct in main tile */}
          <div className="xl:col-span-1 flex items-center justify-center order-2 xl:order-1">
            <div
              className="w-full max-w-[280px] sm:max-w-[320px] lg:max-w-[350px]"
              style={{ overflow: 'visible', position: 'relative' }}
            >
              <VulnerabilityPieChart
                data={pieChartData}
                onSegmentClick={handlePieChartClick}
                selectedSeverity={selectedSeverity}
              />
            </div>
          </div>

          {/* Filter Tile - Nested tile with Figma properties */}
          <div className="xl:col-span-3 order-1 xl:order-2" style={{ overflow: 'visible' }}>
            <div className="bg-neutral-50 dark:bg-[#16161E] rounded-[20px] p-3 sm:p-4 lg:p-6 border border-neutral-200 dark:border-neutral-700 h-full" style={{ overflow: 'visible' }}>
              <VulnerabilityFilters
                filters={filters}
                onFilterChange={handleFilterChange}
                onSubmit={handleFilterSubmit}
                onReset={handleFilterReset}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Vulnerability Table */}
      <div className="overflow-hidden">
        <VulnerabilityDataTable
          vulnerabilities={filteredVulnerabilities}
          onActionClick={handleVulnerabilityAction}
        />
      </div>

      {/* Tooltip example - to be integrated in the pie chart component */}
      <Tooltip
        content={({ active, payload }) =>
          active && payload && payload.length ? (
            <div
              style={{
                background: 'rgba(255,255,255,0.97)',
                color: '#222',
                borderRadius: 8,
                padding: '8px 14px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
                zIndex: 50,
                whiteSpace: 'nowrap',
              }}
            >
              {`${payload[0].name}: ${payload[0].value} (${payload[0].payload.percentage}%)`}
            </div>
          ) : null
        }
      />
    </div>
  );
};

export default VulnerabilityHub;