import React, { useState } from "react";
import { ArrowLeft, Info } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useDashboard } from "../context/DashboardContext";

const AllSecurityHeaders: React.FC = () => {
  const navigate = useNavigate();
  const { rawApiData } = useDashboard();
  const [activeTab, setActiveTab] = useState<'present' | 'missing'>('present');
  const [searchTerm, setSearchTerm] = useState('');

  // Get security misconfigurations from API data
  const securityData = rawApiData?.security_misconfigurations;
  const missingHeaders = securityData?.missing_security_headers?.headers || [];

  // Define all possible security headers (matching dashboard component exactly)
  const allHeaders = [
    {
      name: "content-security-policy",
      description: "Controls resources the user agent is allowed to load for a given page.",
    },
    {
      name: "strict-transport-security",
      description: "Forces connections over HTTPS instead of HTTP.",
    },
    {
      name: "x-frame-options",
      description: "Indicates whether a browser should be allowed to render a page in a frame.",
    },
    {
      name: "x-content-type-options",
      description: "Prevents MIME type sniffing attacks.",
    },
    {
      name: "referrer-policy",
      description: "Controls how much referrer information should be included with requests.",
    },
    {
      name: "permissions-policy",
      description: "Controls which features and APIs can be used in the browser.",
    },
    {
      name: "cross-origin-embedder-policy",
      description: "Prevents a document from loading any cross-origin resources.",
    },
    {
      name: "cross-origin-opener-policy",
      description: "Isolates browsing context exclusively to same-origin documents.",
    },
    {
      name: "cross-origin-resource-policy",
      description: "Conveys a desire that the browser blocks no-cors cross-origin/cross-site requests.",
    },
  ];

  // Determine which headers are present/missing
  const headers = allHeaders.map(header => ({
    ...header,
    present: !missingHeaders.some(missing =>
      missing.toLowerCase().includes(header.name.toLowerCase()) ||
      header.name.toLowerCase().includes(missing.toLowerCase())
    )
  }));

  const presentHeaders = headers.filter(h => h.present);
  const missingHeadersList = headers.filter(h => !h.present);
  const displayHeaders = activeTab === 'present' ? presentHeaders : missingHeadersList;

  // Filter headers based on search term
  const filteredHeaders = displayHeaders.filter(header =>
    header.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    header.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getSeverityClass = (present: boolean) => {
    return present
      ? 'text-success-600 dark:text-success-400'
      : 'text-danger-600 dark:text-danger-400';
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate(-1)}
          className="flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mr-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          Back
        </button>
        <div>
          <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">All Security Headers</h1>
          <p className="text-neutral-600 dark:text-neutral-300">Complete list of security headers and their status</p>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-4">
        {/* Header with search and tabs */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold dark:text-white">Security Headers</h3>
            <div className="tooltip-container relative group">
              <Info className="w-5 h-5 text-neutral-400 hover:text-neutral-600 dark:text-neutral-300 dark:hover:text-neutral-100 cursor-help" />
              <div className="tooltip absolute right-0 top-8 w-64 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block dark:text-neutral-200">
                HTTP security headers that protect against common web vulnerabilities like XSS, clickjacking, and MIME sniffing attacks.
              </div>
            </div>
          </div>

          <div className="relative">
            <input
              type="text"
              className="text-sm border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Search headers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Tabs */}
        <div className="flex mb-4">
          <button
            className={`flex-1 py-2 rounded-tl-lg font-semibold text-xs transition-colors ${
              activeTab === 'present'
                ? 'bg-primary-600 text-white'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-400'
            }`}
            onClick={() => setActiveTab('present')}
          >
            Present Headers ({presentHeaders.length})
          </button>
          <button
            className={`flex-1 py-2 rounded-tr-lg font-semibold text-xs transition-colors ${
              activeTab === 'missing'
                ? 'bg-primary-600 text-white'
                : 'bg-neutral-100 dark:bg-neutral-800 text-neutral-400'
            }`}
            onClick={() => setActiveTab('missing')}
          >
            Missing Headers ({missingHeadersList.length})
          </button>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="data-table w-full">
            <thead>
              <tr>
                <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Header Name</th>
                <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Status</th>
                <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Description</th>
              </tr>
            </thead>
            <tbody>
              {filteredHeaders.length > 0 ? (
                filteredHeaders.map((header) => (
                  <tr
                    key={header.name}
                    className="transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-800"
                  >
                    <td className="font-mono text-neutral-900 dark:text-neutral-100 bg-inherit">{header.name}</td>
                    <td className={`${getSeverityClass(header.present)} bg-inherit`}>
                      <div className="flex items-center">
                        <span className={`w-2 h-2 rounded-full mr-2 ${
                          header.present ? 'bg-green-500' : 'bg-red-500'
                        }`}></span>
                        {header.present ? 'Present' : 'Missing'}
                      </div>
                    </td>
                    <td className="text-neutral-900 dark:text-neutral-100 bg-inherit text-sm">{header.description}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={3} className="text-center py-4 text-neutral-500 dark:text-neutral-300 bg-neutral-100 dark:bg-neutral-900">
                    No headers found matching search criteria
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AllSecurityHeaders;