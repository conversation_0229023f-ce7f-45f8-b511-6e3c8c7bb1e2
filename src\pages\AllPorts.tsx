
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDashboard } from '../context/DashboardContext';
import { ArrowLeft, Info } from 'lucide-react';

const AllPorts: React.FC = () => {
  const navigate = useNavigate();
  const { ports } = useDashboard();
  const [searchTerm, setSearchTerm] = useState('');

  // Filter ports based on search term
  const filteredPorts = ports.filter(port =>
    port.port.toString().includes(searchTerm) ||
    port.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (port.protocol && port.protocol.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getRiskLevelClass = (riskLevel: string) => {
    switch (riskLevel?.toLowerCase()) {
      case 'high': return 'text-danger-600 dark:text-danger-400';
      case 'medium': return 'text-warning-600 dark:text-warning-400';
      case 'low': return 'text-success-600 dark:text-success-400';
      default: return 'text-neutral-600 dark:text-neutral-300';
    }
  };

  const getRiskBadge = (riskLevel: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    switch (riskLevel?.toLowerCase()) {
      case 'high': return `${baseClasses} bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300`;
      case 'medium': return `${baseClasses} bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300`;
      case 'low': return `${baseClasses} bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300`;
      default: return `${baseClasses} bg-neutral-100 text-neutral-700 dark:bg-neutral-800 dark:text-neutral-300`;
    }
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex items-center mb-6">
        <button
          onClick={() => navigate(-1)}
          className="flex items-center text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 mr-4"
        >
          <ArrowLeft className="w-5 h-5 mr-1" />
          Back
        </button>
        <div>
          <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">All Open Ports & Services</h1>
          <p className="text-neutral-600 dark:text-neutral-300">Complete list of detected open ports and running services</p>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-sm border border-neutral-100 dark:border-neutral-800 p-4">
        {/* Header with search */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold dark:text-white">Open Ports & Services</h3>
            <div className="tooltip-container relative group">
              <Info className="w-5 h-5 text-neutral-400 hover:text-neutral-600 dark:text-neutral-300 dark:hover:text-neutral-100 cursor-help" />
              <div className="tooltip absolute right-0 top-8 w-64 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block dark:text-neutral-200">
                Network ports that are currently open and accessible from the internet. Open ports can be entry points for attackers if not properly secured.
              </div>
            </div>
          </div>

          <div className="relative">
            <input
              type="text"
              className="text-sm border border-neutral-300 dark:border-neutral-700 bg-white dark:bg-neutral-800 text-neutral-900 dark:text-neutral-100 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Search ports..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="data-table w-full">
            <thead>
              <tr>
                <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Port</th>
                <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Service</th>
                <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Protocol</th>
                <th className="bg-neutral-50 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-200">Risk Level</th>
              </tr>
            </thead>
            <tbody>
              {filteredPorts.length > 0 ? (
                filteredPorts.map((port) => (
                  <tr
                    key={port.id}
                    className="transition-colors hover:bg-neutral-100 dark:hover:bg-neutral-800"
                  >
                    <td className="font-mono text-neutral-900 dark:text-neutral-100 bg-inherit">{port.port}</td>
                    <td className="text-neutral-900 dark:text-neutral-100 bg-inherit">{port.service}</td>
                    <td className="text-neutral-900 dark:text-neutral-100 bg-inherit uppercase">{port.protocol || 'TCP'}</td>
                    <td className="bg-inherit">
                      <span className={getRiskBadge(port.riskLevel || 'low')}>
                        {(port.riskLevel || 'Low').charAt(0).toUpperCase() + (port.riskLevel || 'low').slice(1)}
                      </span>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="text-center py-4 text-neutral-500 dark:text-neutral-300 bg-neutral-100 dark:bg-neutral-900">
                    No open ports found matching search criteria
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AllPorts;