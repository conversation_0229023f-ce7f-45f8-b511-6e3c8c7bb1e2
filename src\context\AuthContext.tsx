import React, { createContext, useContext, useState, useEffect } from 'react';
import TokenManager from '../utils/tokenManager';

interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  loading: boolean;
}

interface User {
  email: string;
  name?: string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  // Check if user is already logged in on initial load
  useEffect(() => {
    const checkAuthStatus = () => {
      const token = TokenManager.getToken();
      const userData = localStorage.getItem('user_data');

      if (token && userData && !TokenManager.isTokenExpired()) {
        try {
          setIsAuthenticated(true);
          setUser(JSON.parse(userData));
        } catch (error) {
          console.error('Failed to parse user data');
          TokenManager.clearToken();
        }
      } else {
        TokenManager.clearToken();
      }

      setLoading(false);
    };

    checkAuthStatus();
  }, []);

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      setLoading(true);
      
      // Call the login API with the updated base URL
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/users/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      if (!response.ok) {
        throw new Error('Invalid credentials');
      }

      const data = await response.json();

      // Save authentication data with expiration
      TokenManager.setToken(
        data.token,
        data.expiresIn, // Server should provide expiration time
        data.refreshToken
      );

      // Save user data
      const userData: User = {
        email: email,
        name: data.name || email.split('@')[0]
      };
      localStorage.setItem('user_data', JSON.stringify(userData));
      
      // Update state
      setIsAuthenticated(true);
      setUser(userData);
      
      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const logout = () => {
    TokenManager.clearToken();
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, user, login, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
