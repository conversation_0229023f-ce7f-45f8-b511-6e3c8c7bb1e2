import React, { useState, useEffect } from "react";
import { Info, <PERSON>Left, ArrowRight } from "lucide-react";
import { useDashboard } from "../../context/DashboardContext";

const SmallGauge = ({
  value,
  max = 100,
  issues,
  color = "#22c55e",
}: {
  value: number;
  max?: number;
  issues?: number;
  color?: string;
}) => {
  const radius = 65;
  const stroke = 8;
  const centerX = 100;
  const centerY = 80;
  const totalTicks = 48;
  const ticks = [];

  for (let i = 0; i < totalTicks; i++) {
    const angle = 180 + (180 / (totalTicks - 1)) * i;
    const r1 = radius + stroke;
    const r2 = radius + stroke - 18;
    const x1 = centerX + r1 * Math.cos((angle * Math.PI) / 180);
    const y1 = centerY + r1 * Math.sin((angle * Math.PI) / 180);
    const x2 = centerX + r2 * Math.cos((angle * Math.PI) / 180);
    const y2 = centerY + r2 * Math.sin((angle * Math.PI) / 180);
    const isActive = i < Math.round((value / max) * (totalTicks - 1));
    ticks.push(
      <line
        key={i}
        x1={x1}
        y1={y1}
        x2={x2}
        y2={y2}
        stroke={isActive ? color : "#4B5563"}
        strokeWidth={3.5}
        opacity={isActive ? 1 : 0.4}
        strokeLinecap="round"
      />
    );
  }

  return (
    <svg width={200} height={130}>
      <g>
        {ticks}
        <text
          x={centerX}
          y={centerY - 8}
          textAnchor="middle"
          fontSize="2.2rem"
          fontWeight="bold"
          className="fill-neutral-900 dark:fill-white"
        >
          {value}
        </text>
        {typeof issues === "number" && (
          <text
            x={centerX}
            y={centerY + 18}
            textAnchor="middle"
            fontSize="0.8rem"
            className="fill-neutral-500 dark:fill-gray-400"
          >
            {issues} issues
          </text>
        )}
      </g>
    </svg>
  );
};

const BigGauge = ({
  value,
  max = 100,
  color = "#22c55e",
}: {
  value: number;
  max?: number;
  color?: string;
}) => {
  const radius = 80;
  const stroke = 20;
  const center = 100;
  const circumference = Math.PI * radius;
  const offset = circumference * (1 - value / max);

  return (
    <svg width={200} height={140}>
      <g>
        <path
          d={`M 20 ${center} A ${radius} ${radius} 0 0 1 180 ${center}`}
          fill="none"
          stroke="#4B5563"
          strokeWidth={stroke}
          strokeLinecap="round"
          opacity={0.4}
        />
        <path
          d={`M 20 ${center} A ${radius} ${radius} 0 0 1 180 ${center}`}
          fill="none"
          stroke={color}
          strokeWidth={stroke}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          style={{ transition: "stroke-dashoffset 0.5s" }}
        />
      </g>
      <text
        x={center}
        y={center + 25}
        textAnchor="middle"
        fontSize="2.2rem"
        fontWeight="bold"
        className="fill-neutral-900 dark:fill-white"
      >
        {value}
      </text>
    </svg>
  );
};

interface ComplianceFramework {
  framework: string;
  covered: number;
  total: number;
}

interface ComplianceMappingProps {
  compliance?: ComplianceFramework[];
}

const complianceColors = [
  "#22c55e", // green
  "#eab308", // yellow
  "#f59e42", // orange
  "#ef4444", // red
  "#0ea5e9", // blue
  "#a78bfa", // purple
];

const complianceLabels: Record<string, string> = {
  "CIS Compliance": "CIS Compliance",
  "OWASP Top 10": "A05:2021-Security Misconfiguration",
};

const ComplianceMapping: React.FC<ComplianceMappingProps> = ({
  compliance: propCompliance,
}) => {
  const { stats, vulnerabilities, rawApiData } = useDashboard();
  const [compliance, setCompliance] = useState<ComplianceFramework[]>([]);
  const [current, setCurrent] = useState(0);

  useEffect(() => {
    if (propCompliance) {
      setCompliance(propCompliance);
    } else {
      // Get compliance data from API response
      const complianceData = rawApiData?.compliance_readiness;

      if (complianceData) {
        // Use real API data
        const cisCompliance = complianceData.cis_compliance || 0;
        const owaspCompliance = complianceData.owasp_compliance || 0;
        const overallCompliance = complianceData.overall_compliance_score || 0;
        const cisIssues = complianceData.total_cis_issues || 0;
        const owaspIssues = complianceData.total_owasp_issues || 0;

        const generatedCompliance: ComplianceFramework[] = [
          {
            framework: "CIS Compliance",
            covered: Math.round((cisCompliance / 100) * 20),
            total: 20,
          },
          {
            framework: "OWASP Top 10",
            covered: Math.round((owaspCompliance / 100) * 10),
            total: 10,
          },
        
        ];
        setCompliance(generatedCompliance);
      } else {
        // Fallback calculation based on vulnerability data
        const totalVulns = stats.vulnerabilities.total;
        const highSeverityVulns = stats.vulnerabilities.highSeverity;
        const mediumSeverityVulns = stats.vulnerabilities.mediumSeverity;

        const complianceImpact = (highSeverityVulns * 10) + (mediumSeverityVulns * 5) + (totalVulns * 2);
        const baseScore = Math.max(0, 100 - complianceImpact);

        const cweCount = vulnerabilities.filter(v =>
          v.complianceMapping && v.complianceMapping.some(mapping => mapping.startsWith('CWE-'))
        ).length;

        const owaspIssues = stats.complianceViolations || 0;
        const owaspCoverage = Math.max(0, 10 - Math.floor(owaspIssues / 2));

        const generatedCompliance: ComplianceFramework[] = [
          {
            framework: "CIS Compliance",
            covered: Math.round((baseScore / 100) * 20),
            total: 20,
          },
          {
            framework: "OWASP Top 10",
            covered: owaspCoverage,
            total: 10,
          },
        ];
        setCompliance(generatedCompliance);
      }
    }
  }, [propCompliance, stats, vulnerabilities, rawApiData]);

  const currentCompliance = compliance[current] || {
    framework: "",
    covered: 0,
    total: 1,
  };
  const percentage = Number(
    ((currentCompliance.covered / currentCompliance.total) * 100).toFixed(2)
  );
  const color = complianceColors[current % complianceColors.length];

  return (
    <div className="bg-white w-full dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323]">
      <div className="p-4 sm:p-6 flex flex-col">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 lg:mb-4 gap-2">
          <div className="flex items-center gap-1 mr-3">
            <h3 className="text-base sm:text-lg lg:text-2xl font-bold text-neutral-900 dark:text-white">
              Compliance Coverage
            </h3>
            <div className="tooltip-container relative group">
              <Info className="w-4 h-4 text-neutral-500 dark:text-gray-400 cursor-help" />
              <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
                Compliance coverage across major security frameworks including CIS, OWASP. Shows how well your security posture aligns with industry standards.
              </div>
            </div>
          </div>
          {/* <button className="bg-transparent border border-neutral-400 dark:border-gray-600 rounded-md px-3 py-1.5 text-xs text-neutral-700 dark:text-gray-300 hover:border-neutral-500 dark:hover:border-gray-500 transition-colors">
            View Details
          </button> */}
        </div>

        <div className="flex flex-col lg:flex-row flex-1 items-stretch justify-evenly gap-4 lg:gap-6">
          {/* Left Side - CIS Compliance Card */}
          <div className="flex flex-col items-center flex-1 min-w-[220px] max-w-full">
            <div
              className="rounded-xl p-4 flex flex-col items-center w-full min-w-[180px] sm:min-w-[220px] max-w-[340px] h-[160px] mb-2 border border-neutral-300 dark:border-[#666666]"
            >
              <div className="flex items-center gap-2 mb-3 w-full justify-between">
                <span className="font-medium text-neutral-900 dark:text-white text-base">
                  {currentCompliance.framework}
                </span>
                <div className="tooltip-container relative group">
                  <Info className="w-4 h-4 text-neutral-500 dark:text-gray-400 cursor-help" />
                  <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
                    {currentCompliance.framework === "CIS Compliance" && "Center for Internet Security controls for securing IT systems and data."}
                    {currentCompliance.framework === "OWASP Top 10" && "Open Web Application Security Project's top 10 most critical web application security risks."}
                  </div>
                </div>
              </div>
              <div className="flex-1 flex items-center justify-center">
                <SmallGauge
                  value={Math.round(percentage)}
                  issues={currentCompliance.total - currentCompliance.covered}
                  color={color}
                />
              </div>
            </div>
            {/* Navigation arrows */}
            <div className="flex gap-3 items-center mt-2">
              <button
                className="rounded-full bg-neutral-300 dark:bg-neutral-700 hover:bg-neutral-400 dark:hover:bg-neutral-600 p-2 transition-colors"
                onClick={() =>
                  setCurrent((prev) =>
                    prev === 0 ? compliance.length - 1 : prev - 1
                  )
                }
                aria-label="Previous"
              >
                <ArrowLeft className="w-4 h-4 text-neutral-700 dark:text-white" />
              </button>
              <button
                className="rounded-full bg-neutral-300 dark:bg-neutral-700 hover:bg-neutral-400 dark:hover:bg-neutral-600 p-2 transition-colors"
                onClick={() =>
                  setCurrent((prev) =>
                    prev === compliance.length - 1 ? 0 : prev + 1
                  )
                }
                aria-label="Next"
              >
                <ArrowRight className="w-4 h-4 text-neutral-700 dark:text-white" />
              </button>
            </div>
          </div>

          {/* Right Side - Big Gauge */}
          <div className="flex flex-col items-center flex-1 min-w-[180px] sm:min-w-[220px] max-w-full justify-center mt-4 lg:mt-0">
            <div className="mb-2">
              <BigGauge value={Number((percentage * 0.97).toFixed(2))} color={color} />
            </div>
            <div className="text-xs sm:text-sm md:text-base font-medium text-neutral-600 dark:text-gray-400 text-center max-w-xs">
              {complianceLabels[currentCompliance.framework] || currentCompliance.framework}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceMapping;