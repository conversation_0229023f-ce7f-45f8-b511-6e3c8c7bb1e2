import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Clock, ExternalLink, Shield, AlertTriangle, Bug, Zap, RefreshCw, Wifi } from 'lucide-react';
import { NewsArticle } from '../../types/types';
import ENV_CONFIG from '../../utils/envConfig';

interface NewsItem {
  id: string;
  title: string;
  description: string;
  source: string;
  publishedAt: string;
  category: 'vulnerability' | 'breach' | 'malware' | 'update' | 'threat';
  severity: 'critical' | 'high' | 'medium' | 'low';
  url: string;
  attachments?: {
    name: string;
    type: string;
  }[];
}

const CybersecurityNews: React.FC = React.memo(() => {
  const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [autoRefreshCountdown, setAutoRefreshCountdown] = useState(180); // 3 minutes for more live updates
  const [isLive, setIsLive] = useState(true);

  // Memoized function to determine category based on title/content
  const categorizeNews = useCallback((title: string, description: string): { category: NewsItem['category'], severity: NewsItem['severity'] } => {
    const content = (title + ' ' + description).toLowerCase();

    if (content.includes('vulnerability') || content.includes('cve') || content.includes('zero-day') || content.includes('exploit')) {
      const severity = content.includes('critical') || content.includes('zero-day') ? 'critical' :
                     content.includes('high') ? 'high' : 'medium';
      return { category: 'vulnerability', severity };
    }

    if (content.includes('breach') || content.includes('hack') || content.includes('attack') || content.includes('ransomware')) {
      const severity = content.includes('major') || content.includes('massive') ? 'high' : 'medium';
      return { category: 'breach', severity };
    }

    if (content.includes('malware') || content.includes('trojan') || content.includes('virus') || content.includes('phishing')) {
      return { category: 'malware', severity: 'high' };
    }

    if (content.includes('update') || content.includes('patch') || content.includes('fix')) {
      return { category: 'update', severity: 'medium' };
    }

    return { category: 'threat', severity: 'medium' };
  }, []);

  // Function to fetch news from multiple APIs
  const fetchCybersecurityNews = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const newsApiKey = ENV_CONFIG.NEWS_API_KEY;
      const gNewsApiKey = ENV_CONFIG.GNEWS_API_KEY;

      let allArticles: NewsArticle[] = [];

      // Try NewsData.io API first (often has better URLs)
      try {
        const newsDataKey = ENV_CONFIG.NEWSDATA_API_KEY;
        if (newsDataKey) {
          const newsDataResponse = await fetch(
            `https://newsdata.io/api/1/news?apikey=${newsDataKey}&q=cybersecurity OR vulnerability OR "data breach" OR ransomware&language=en&category=technology`
          );

          if (newsDataResponse.ok) {
            const newsDataResult = await newsDataResponse.json();
            console.log('NewsData.io Response:', newsDataResult);
            if (newsDataResult.results) {
              console.log('Sample NewsData articles:', newsDataResult.results.slice(0, 3));
              allArticles = [...allArticles, ...newsDataResult.results.map((article: any) => ({
                ...article,
                url: article.link || article.url,
                source: { name: article.source_id || 'NewsData' },
                publishedAt: article.pubDate
              }))];
            }
          }
        }
      } catch (err) {
        console.warn('NewsData.io failed:', err);
      }

      // Try NewsAPI as backup
      if (newsApiKey && allArticles.length < 5) {
        try {
          const newsApiResponse = await fetch(
            `https://newsapi.org/v2/everything?q=cybersecurity&sortBy=publishedAt&pageSize=10&apiKey=${newsApiKey}`
          );

          if (newsApiResponse.ok) {
            const newsApiData = await newsApiResponse.json();
            console.log('NewsAPI Response:', newsApiData);
            if (newsApiData.articles) {
              console.log('Sample NewsAPI articles:', newsApiData.articles.slice(0, 3));
              allArticles = [...allArticles, ...newsApiData.articles];
            }
          }
        } catch (err) {
          console.warn('NewsAPI failed:', err);
        }
      }

      // Try GNews API as final backup
      if (gNewsApiKey && allArticles.length < 3) {
        try {
          const gNewsResponse = await fetch(
            `https://gnews.io/api/v4/search?q=cybersecurity&lang=en&max=10&apikey=${gNewsApiKey}`
          );

          if (gNewsResponse.ok) {
            const gNewsData = await gNewsResponse.json();
            console.log('GNews Response:', gNewsData);
            if (gNewsData.articles) {
              console.log('Sample GNews articles:', gNewsData.articles.slice(0, 3));
              allArticles = [...allArticles, ...gNewsData.articles];
            }
          }
        } catch (err) {
          console.warn('GNews API failed:', err);
        }
      }

      // Process and format the articles
      const processedNews: NewsItem[] = allArticles
        .filter(article =>
          article.title &&
          article.description &&
          article.title !== '[Removed]' &&
          article.url &&
          article.url !== '' &&
          !article.url.includes('localhost') &&
          article.url.startsWith('http')
        )
        .slice(0, 4)
        .map((article, index) => {
          const { category, severity } = categorizeNews(article.title, article.description || '');

          // Ensure URL is valid and external
          let articleUrl = article.url || article.link || article.href;

          // Handle different API response structures
          if (!articleUrl && article.source && article.source.url) {
            articleUrl = article.source.url;
          }

          // Validate and clean the URL
          if (!articleUrl ||
              articleUrl === '#' ||
              articleUrl.includes('localhost') ||
              !articleUrl.startsWith('http')) {
            // Fallback to a search URL if original URL is invalid
            const searchQuery = encodeURIComponent(`"${article.title}" ${article.source?.name || ''}`);
            articleUrl = `https://www.google.com/search?q=${searchQuery}`;
          }

          console.log('Processing article:', {
            title: article.title,
            originalUrl: article.url,
            linkField: article.link,
            sourceUrl: article.source?.url,
            finalUrl: articleUrl,
            source: article.source?.name,
            fullArticle: article
          });

          return {
            id: `news-${index}`,
            title: article.title,
            description: article.description || 'No description available',
            source: article.source?.name || 'Unknown Source',
            publishedAt: article.publishedAt || new Date().toISOString(),
            category,
            severity,
            url: articleUrl,
            attachments: category === 'vulnerability' ? [
              { name: 'Security_Advisory.pdf', type: 'pdf' }
            ] : category === 'breach' ? [
              { name: 'Incident_Report.pdf', type: 'pdf' },
              { name: 'IOCs.csv', type: 'csv' }
            ] : undefined
          };
        });

      if (processedNews.length === 0) {
        // Fallback to curated real cybersecurity articles with working URLs
        const fallbackNews: NewsItem[] = [
          {
            id: 'fallback-1',
            title: 'CISA Releases Security Advisory for Critical Infrastructure',
            description: 'The Cybersecurity and Infrastructure Security Agency has issued new guidance for protecting critical infrastructure from cyber threats...',
            source: 'CISA',
            publishedAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            category: 'vulnerability',
            severity: 'critical',
            url: 'https://www.cisa.gov/news-events/alerts',
            attachments: [{ name: 'CISA_Advisory.pdf', type: 'pdf' }]
          },
          {
            id: 'fallback-2',
            title: 'Latest Cybersecurity Threats and Vulnerabilities',
            description: 'Comprehensive coverage of the latest security threats, data breaches, and vulnerability disclosures affecting organizations worldwide...',
            source: 'Bleeping Computer',
            publishedAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            category: 'breach',
            severity: 'high',
            url: 'https://www.bleepingcomputer.com/news/security/',
            attachments: [{ name: 'Threat_Report.pdf', type: 'pdf' }]
          },
          {
            id: 'fallback-3',
            title: 'Security Week - Latest Cybersecurity News',
            description: 'Breaking news and analysis on cybersecurity threats, vulnerabilities, and industry developments from security experts...',
            source: 'Security Week',
            publishedAt: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
            category: 'update',
            severity: 'medium',
            url: 'https://www.securityweek.com/',
          },
          {
            id: 'fallback-4',
            title: 'Krebs on Security - In-depth Security Analysis',
            description: 'Investigative reporting on cybercrime, security breaches, and the latest threats from renowned security journalist Brian Krebs...',
            source: 'KrebsOnSecurity',
            publishedAt: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString(),
            category: 'threat',
            severity: 'high',
            url: 'https://krebsonsecurity.com/',
          }
        ];
        setNewsItems(fallbackNews);
      } else {
        setNewsItems(processedNews);
      }

    } catch (err) {
      console.error('Error fetching cybersecurity news:', err);
      setError('Failed to load security news');
    } finally {
      setLoading(false);
      setLastUpdated(new Date());
      setAutoRefreshCountdown(180); // Reset countdown to 3 minutes
    }
  }, []);

  // Auto-refresh countdown effect - optimized with useCallback
  const handleCountdown = useCallback(() => {
    setAutoRefreshCountdown((prev) => {
      if (prev <= 1) {
        fetchCybersecurityNews();
        return 180; // Reset to 3 minutes
      }
      return prev - 1;
    });
  }, [fetchCybersecurityNews]);

  useEffect(() => {
    const interval = setInterval(handleCountdown, 1000); // Update every second
    return () => clearInterval(interval);
  }, [handleCountdown]);

  // Initial load
  useEffect(() => {
    fetchCybersecurityNews();
  }, [fetchCybersecurityNews]);

  // Memoized helper functions for better performance
  const getCategoryIcon = useCallback((category: string) => {
    switch (category) {
      case 'vulnerability':
        return <Bug className="w-5 h-5" />;
      case 'breach':
        return <AlertTriangle className="w-5 h-5" />;
      case 'malware':
        return <Zap className="w-5 h-5" />;
      case 'update':
        return <Shield className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  }, []);

  const getCategoryColor = useCallback((category: string) => {
    switch (category) {
      case 'vulnerability':
        return 'bg-danger-500';
      case 'breach':
        return 'bg-warning-500';
      case 'malware':
        return 'bg-purple-500';
      case 'update':
        return 'bg-primary-500';
      default:
        return 'bg-neutral-500';
    }
  }, []);

  const getSeverityColor = useCallback((severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-500';
      case 'high':
        return 'text-orange-500';
      case 'medium':
        return 'text-yellow-500';
      case 'low':
        return 'text-green-500';
      default:
        return 'text-gray-500';
    }
  }, []);

  // Memoized time formatting function
  const formatTimeAgo = useCallback((dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  }, []);

  // Memoized news items to prevent unnecessary re-renders
  const memoizedNewsItems = useMemo(() => newsItems, [newsItems]);

  if (loading) {
    return (
      <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-6 h-full">
        <div className="animate-pulse">
          <div className="h-6 bg-neutral-200 dark:bg-neutral-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-start space-x-3">
                <div className="w-10 h-10 bg-neutral-200 dark:bg-neutral-700 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-neutral-200 dark:bg-neutral-700 rounded w-full"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-6 h-full flex flex-col items-center justify-center">
        <AlertTriangle className="w-8 h-8 text-orange-500 mb-2" />
        <p className="text-sm text-neutral-600 dark:text-neutral-300 mb-4">{error}</p>
        <button
          onClick={fetchCybersecurityNews}
          className="flex items-center space-x-2 px-3 py-1.5 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors text-sm"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Retry</span>
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white dark:bg-[#202020] rounded-[10px] border border-neutral-200 dark:border-[#232323] p-6 h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <div className="flex items-center space-x-2">
            <h3 className="text-lg font-semibold text-neutral-900 dark:text-white">
              Latest Security News
            </h3>
            <div className="flex items-center space-x-1">
              <Wifi className={`w-4 h-4 ${isLive ? 'text-success-500 animate-pulse' : 'text-neutral-400'}`} />
              <span className="text-xs text-success-500 font-medium">LIVE</span>
            </div>
          </div>
          <div className="flex items-center space-x-3 mt-1">
            <p className="text-sm text-neutral-500 dark:text-neutral-400">
              Real-time cybersecurity threats and updates
            </p>
            <div className="flex items-center space-x-1 text-xs text-neutral-400 dark:text-neutral-500">
              <Clock className="w-3 h-3" />
              <span>
                Next update: {Math.floor(autoRefreshCountdown / 60)}:{(autoRefreshCountdown % 60).toString().padStart(2, '0')}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={fetchCybersecurityNews}
            disabled={loading}
            className="p-1.5 border border-neutral-400 dark:border-neutral-600 rounded-md hover:border-neutral-500 transition-colors disabled:opacity-50"
            title="Refresh news"
          >
            <RefreshCw className={`w-4 h-4 text-neutral-600 dark:text-neutral-300 ${loading ? 'animate-spin' : ''}`} />
          </button>
          {/* <button className="border border-neutral-400 dark:border-neutral-600 rounded-md px-3 py-1.5 text-xs text-neutral-700 dark:text-neutral-300 hover:border-neutral-500 transition-colors">
            View More
          </button> */}
        </div>
      </div>

      {/* News Items */}
      <div className="flex-1 space-y-4 overflow-y-auto">
        {memoizedNewsItems.map((item, index) => (
          <div
            key={item.id}
            onClick={() => {
              console.log('Clicking news item:', item.title, 'URL:', item.url);
              if (item.url && item.url !== '#') {
                window.open(item.url, '_blank', 'noopener,noreferrer');
              } else {
                console.error('Invalid URL for article:', item.title);
                // Fallback to Google search
                const searchQuery = encodeURIComponent(item.title + ' ' + item.source);
                window.open(`https://www.google.com/search?q=${searchQuery}`, '_blank', 'noopener,noreferrer');
              }
            }}
            className="flex items-start space-x-3 p-3 rounded-lg hover:bg-neutral-50 dark:hover:bg-neutral-800 transition-all duration-300 group transform hover:scale-[1.02] hover:shadow-sm animate-slide-in-up cursor-pointer"
            style={{
              animationDelay: `${index * 0.1}s`
            }}
            title={`Click to read full article - ${item.url}`}
          >
            {/* Category Icon */}
            <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${getCategoryColor(item.category)} flex-shrink-0`}>
              {getCategoryIcon(item.category)}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="text-sm font-semibold text-neutral-900 dark:text-white line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors group-hover:underline">
                    {item.title}
                  </h4>
                  <p className="text-xs text-neutral-600 dark:text-neutral-300 mt-1 line-clamp-2">
                    {item.description}
                  </p>
                </div>
                
                {/* Action Button */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    console.log('External link button clicked:', item.title, 'URL:', item.url);
                    if (item.url && item.url !== '#') {
                      window.open(item.url, '_blank', 'noopener,noreferrer');
                    } else {
                      const searchQuery = encodeURIComponent(item.title + ' ' + item.source);
                      window.open(`https://www.google.com/search?q=${searchQuery}`, '_blank', 'noopener,noreferrer');
                    }
                  }}
                  className="ml-2 p-1.5 rounded-md border border-neutral-300 dark:border-neutral-600 hover:border-primary-500 dark:hover:border-primary-400 transition-colors flex-shrink-0"
                  title={`Open article in new tab - ${item.url}`}
                >
                  <ExternalLink className="w-3 h-3 text-neutral-500 dark:text-neutral-400" />
                </button>
              </div>

              {/* Attachments */}
              {item.attachments && item.attachments.length > 0 && (
                <div className="flex items-center space-x-3 mt-2">
                  {item.attachments.slice(0, 2).map((attachment, index) => (
                    <div key={index} className="flex items-center space-x-1 text-xs text-neutral-500 dark:text-neutral-400">
                      <div className="w-3 h-3 bg-neutral-300 dark:bg-neutral-600 rounded"></div>
                      <span className="truncate max-w-[100px]">{attachment.name}</span>
                    </div>
                  ))}
                  {item.attachments.length > 2 && (
                    <span className="text-xs text-primary-600 dark:text-primary-400">
                      +{item.attachments.length - 2} more
                    </span>
                  )}
                </div>
              )}

              {/* Meta Info */}
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center space-x-2 text-xs text-neutral-500 dark:text-neutral-400">
                  <span>{item.source}</span>
                  <span>•</span>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>{formatTimeAgo(item.publishedAt)}</span>
                  </div>
                  <span>•</span>
                  <div className="flex items-center space-x-1 text-primary-500 dark:text-primary-400 group-hover:text-primary-600 dark:group-hover:text-primary-300">
                    <ExternalLink className="w-3 h-3" />
                    <span className="font-medium">Read more</span>
                  </div>
                </div>
                <span className={`text-xs font-medium ${getSeverityColor(item.severity)}`}>
                  {item.severity.toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
});

export default CybersecurityNews;
