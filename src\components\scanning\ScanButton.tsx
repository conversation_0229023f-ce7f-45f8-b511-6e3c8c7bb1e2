import React, { useState } from 'react';
import scanApi from '../../services/apiService';
import { useDashboard } from '../../context/DashboardContext';

interface ScanButtonProps {
  onScanComplete?: (data: any) => void;
  onScanError?: (error: Error) => void;
}

const ScanButton: React.FC<ScanButtonProps> = ({ onScanComplete, onScanError }) => {
  const [isScanning, setIsScanning] = useState(false);
  const [urlToScan, setUrlToScan] = useState('https://ginandjuice.shop');
  const { refreshDashboard } = useDashboard();

  const handleScan = async () => {
    if (!urlToScan.trim()) {
      if (onScanError) onScanError(new Error('Please enter a valid URL'));
      return;
    }

    setIsScanning(true);
    try {
      const result = await scanApi.startScan(urlToScan);
      
      // Refresh dashboard with new data
      refreshDashboard();
      
      if (onScanComplete) {
        onScanComplete(result);
      }
    } catch (error) {
      console.error('Scan failed:', error);
      if (onScanError) {
        onScanError(error as Error);
      }
    } finally {
      setIsScanning(false);
    }
  };

  return (
    <button
      onClick={handleScan}
      disabled={isScanning}
      className="px-4 py-2 bg-primary-500 text-white rounded-md hover:bg-primary-600 transition-colors"
    >
      {isScanning ? 'Scanning...' : 'Start Scan'}
    </button>
  );
};

export default ScanButton;