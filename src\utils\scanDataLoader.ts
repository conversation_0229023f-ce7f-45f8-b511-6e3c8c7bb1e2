// // Utility function to load and parse the scan.json data
// import scanData from '../../scan.json';

// export interface ScanData {
//   alerts: any[];
//   attack_surface_index: {
//     metrics: {
//       detected_subdomains: string[];
//       exposed_services: string[];
//       exposed_services_count: number;
//       open_ports: number[];
//       open_ports_count: number;
//       public_ips: string[];
//       public_ips_count: number;
//       subdomains_count: number;
//     };
//     score: number;
//   };
//   combined_security_score: number;
//   compliance_readiness: {
//     cis_compliance: number;
//     cis_findings: {
//       [key: string]: number;
//     };
//     overall_compliance_score: number;
//     owasp_compliance: number;
//     owasp_findings: {
//       [key: string]: number;
//     };
//     total_cis_issues: number;
//     total_owasp_issues: number;
//   };
//   cyber_hygiene_score: {
//     grade: string;
//     metrics: {
//       critical_issues: number;
//       high_issues: number;
//       low_issues: number;
//       medium_issues: number;
//       missing_security_headers: boolean;
//     };
//     score: number;
//   };
//   endpoints: {
//     endpoints: string[];
//     total_count: number;
//   };
//   scan_date: string;
//   security_misconfigurations: {
//     insecure_cookies: number;
//     missing_security_headers: {
//       count: number;
//       headers: any[];
//     };
//     other_misconfigurations: number;
//     total_misconfigurations: number;
//     unsafe_cors_configurations: number;
//   };
//   threat_intelligence: {
//     reputation_data: {
//       analysis_stats: {
//         harmless: number;
//         malicious: number;
//         suspicious: number;
//         total_engines: number;
//         undetected: number;
//       };
//       categories: any;
//       first_seen: any;
//       last_analysis: string;
//       reputation_score: number;
//       risk_assessment: string;
//       status: string;
//       threat_score: number;
//     };
//     threat_intelligence_score: {
//       assessment: string;
//       confidence: string;
//       score: number;
//     };
//   };
//   url: string;
//   vendor_risk_rating: {
//     components: {
//       access_scope: number;
//       compliance_readiness: number;
//       data_sensitivity: number;
//       remediation_behavior: number;
//       security_testing: number;
//       threat_intelligence: number;
//     };
//     letter_grade: string;
//     numeric_rating: number;
//     recommendation: string;
//     risk_level: string;
//     weighted_score: number;
//   };
// }

// export const getScanData = (): ScanData => {
//   return scanData as ScanData;
// };

// export default getScanData;