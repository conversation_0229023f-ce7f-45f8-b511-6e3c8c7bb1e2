import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, FileText, Settings, Target, Search, Factory, AlertOctagon } from 'lucide-react';
import { motion } from 'framer-motion';

const Sidebar: React.FC = () => {
  const location = useLocation();
  const [expanded, setExpanded] = useState(false);

  const menuItems = [
    { icon: Home, label: 'Dashboard', path: '/' },
    { icon: Target, label: 'Assessment Center', path: '/assessment' },
    { icon: Search, label: 'Assets Inventory', path: '/assets' },
    { icon: Factory, label: 'Remidation Factory', path: '/remidation-factory' },
    { icon: AlertOctagon, label: 'Vulnerability Hub', path: '/vulnerability-hub' },
    { icon: FileText, label: 'Reports', path: '/reports' },
    { icon: Settings, label: 'Settings', path: '/settings' },
  ];

  return (
    <div
      className={`hidden lg:block fixed top-14 sm:top-16 lg:top-18 left-0 bottom-0 z-50 shadow-sm transition-all duration-300 ease-in-out ${
        expanded ? 'w-56' : 'w-16'
      }`}
      onMouseEnter={() => setExpanded(true)}
      onMouseLeave={() => setExpanded(false)}
      style={{
        backgroundColor: 'var(--sidebar-bg)',
        borderRight: '1px solid var(--sidebar-border)'
      }}
    >
      <div className="py-5 flex flex-col h-full">
        {menuItems.map((item, index) => (
          <Link
            key={index}
            to={item.path}
            className={`relative flex items-center h-12 w-full px-4 rounded-md transition-all duration-200`}
            style={{
              color: location.pathname === item.path ? '#ffffff' : 'var(--sidebar-text)',
              backgroundColor: location.pathname === item.path ? '#00457f' : 'transparent'
            }}
            onMouseEnter={(e) => {
              if (location.pathname !== item.path) {
                e.currentTarget.style.backgroundColor = '#00457f';
                e.currentTarget.style.color = '#ffffff';
              }
            }}
            onMouseLeave={(e) => {
              if (location.pathname !== item.path) {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.color = 'var(--sidebar-text)';
              }
            }}
          >
            <motion.div
              whileHover={{ scale: 1.1 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <item.icon className="w-5 h-5 flex-shrink-0" />
            </motion.div>
            <span
              className={`ml-3 whitespace-nowrap transition-opacity duration-200 ${
                expanded ? 'opacity-100' : 'opacity-0'
              }`}
            >
              {item.label}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;