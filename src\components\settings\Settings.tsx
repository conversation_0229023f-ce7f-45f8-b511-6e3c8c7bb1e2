import React from 'react';
import { Settings as SettingsIcon, <PERSON>, Lock, Database, Server, Clock } from 'lucide-react';

const Settings: React.FC = () => {
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Settings</h1>
        <p className="text-neutral-600 dark:text-neutral-300">Configure your AIPT Dashboard preferences</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-1">
          <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-card p-5 sticky top-24">
            <h3 className="text-lg font-semibold mb-4 flex items-center dark:text-neutral-100">
              <SettingsIcon className="w-5 h-5 mr-2" />
              Settings
            </h3>
            
            <div className="space-y-1">
              <button className="w-full text-left py-2 px-3 rounded-md bg-primary-50 dark:bg-primary-900 text-primary-700 dark:text-primary-200 font-medium">
                Notifications
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md text-neutral-700 dark:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-neutral-800">
                Security
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md text-neutral-700 dark:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-neutral-800">
                Data Retention
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md text-neutral-700 dark:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-neutral-800">
                API Configuration
              </button>
              <button className="w-full text-left py-2 px-3 rounded-md text-neutral-700 dark:text-neutral-100 hover:bg-neutral-50 dark:hover:bg-neutral-800">
                Scan Schedule
              </button>
            </div>
          </div>
        </div>
        
        <div className="md:col-span-2">
          <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-card p-5 mb-6">
            <div className="flex items-center mb-4">
              <Bell className="w-5 h-5 text-primary-500 mr-2" />
              <h3 className="text-lg font-semibold dark:text-neutral-100">Notification Settings</h3>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium dark:text-neutral-100">Email Notifications</h4>
                  <p className="text-sm text-neutral-600 dark:text-neutral-300">Receive scan results and security alerts via email</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" value="" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-neutral-300 dark:bg-neutral-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-neutral-900 after:border-neutral-300 dark:after:border-neutral-700 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium dark:text-neutral-100">High Severity Alerts</h4>
                  <p className="text-sm text-neutral-600 dark:text-neutral-300">Immediate notification for high severity findings</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" value="" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-neutral-300 dark:bg-neutral-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-neutral-900 after:border-neutral-300 dark:after:border-neutral-700 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium dark:text-neutral-100">Weekly Summary Report</h4>
                  <p className="text-sm text-neutral-600 dark:text-neutral-300">Receive a weekly summary of security findings</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" value="" className="sr-only peer" />
                  <div className="w-11 h-6 bg-neutral-300 dark:bg-neutral-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-neutral-900 after:border-neutral-300 dark:after:border-neutral-700 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium dark:text-neutral-100">Desktop Notifications</h4>
                  <p className="text-sm text-neutral-600 dark:text-neutral-300">Show browser notifications for security events</p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" value="" className="sr-only peer" defaultChecked />
                  <div className="w-11 h-6 bg-neutral-300 dark:bg-neutral-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-neutral-900 after:border-neutral-300 dark:after:border-neutral-700 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                </label>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-card p-5">
              <div className="flex items-center mb-4">
                <Lock className="w-5 h-5 text-primary-500 mr-2" />
                <h3 className="text-lg font-semibold dark:text-neutral-100">Security</h3>
              </div>
              
              <div className="space-y-3">
                <button className="w-full py-2 px-4 bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 text-neutral-700 dark:text-neutral-100 rounded text-sm text-left transition-colors">
                  Change Password
                </button>
                <button className="w-full py-2 px-4 bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 text-neutral-700 dark:text-neutral-100 rounded text-sm text-left transition-colors">
                  Two-Factor Authentication
                </button>
                <button className="w-full py-2 px-4 bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 text-neutral-700 dark:text-neutral-100 rounded text-sm text-left transition-colors">
                  API Keys Management
                </button>
              </div>
            </div>
            
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-card p-5">
              <div className="flex items-center mb-4">
                <Database className="w-5 h-5 text-primary-500 mr-2" />
                <h3 className="text-lg font-semibold dark:text-neutral-100">Data Retention</h3>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium dark:text-neutral-100">Keep scan data for</h4>
                  </div>
                  <select className="text-sm border border-neutral-300 dark:border-neutral-700 rounded px-3 py-1.5 bg-white dark:bg-neutral-900 text-neutral-700 dark:text-neutral-100">
                    <option>30 days</option>
                    <option>90 days</option>
                    <option>1 year</option>
                    <option>Forever</option>
                  </select>
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium dark:text-neutral-100">Auto-archive reports</h4>
                  </div>
                  <select className="text-sm border border-neutral-300 dark:border-neutral-700 rounded px-3 py-1.5 bg-white dark:bg-neutral-900 text-neutral-700 dark:text-neutral-100">
                    <option>After 30 days</option>
                    <option>After 90 days</option>
                    <option>Never</option>
                  </select>
                </div>
                
                <button className="w-full py-2 px-4 bg-danger-50 dark:bg-danger-900 hover:bg-danger-100 dark:hover:bg-danger-800 text-danger-700 dark:text-danger-200 rounded text-sm text-left transition-colors mt-3">
                  Clear All Data
                </button>
              </div>
            </div>
            
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-card p-5">
              <div className="flex items-center mb-4">
                <Server className="w-5 h-5 text-primary-500 mr-2" />
                <h3 className="text-lg font-semibold dark:text-neutral-100">API Configuration</h3>
              </div>
              
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium mb-1 dark:text-neutral-100">API Endpoint</label>
                  <input 
                    type="text" 
                    className="w-full text-sm border border-neutral-300 dark:border-neutral-700 rounded px-3 py-1.5 bg-white dark:bg-neutral-900 text-neutral-700 dark:text-neutral-100"
                    defaultValue="https://api.aipt.example.com/v1"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1 dark:text-neutral-100">API Key</label>
                  <div className="flex">
                    <input 
                      type="password" 
                      className="flex-1 text-sm border border-neutral-300 dark:border-neutral-700 rounded-l px-3 py-1.5 bg-white dark:bg-neutral-900 text-neutral-700 dark:text-neutral-100"
                      defaultValue="●●●●●●●●●●●●●●●●●●●●"
                    />
                    <button className="bg-primary-500 text-white px-3 rounded-r text-sm">
                      Show
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-white dark:bg-neutral-900 rounded-lg shadow-card p-5">
              <div className="flex items-center mb-4">
                <Clock className="w-5 h-5 text-primary-500 mr-2" />
                <h3 className="text-lg font-semibold dark:text-neutral-100">Scan Schedule</h3>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="text-sm font-medium dark:text-neutral-100">Automatic Scanning</h4>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" value="" className="sr-only peer" defaultChecked />
                    <div className="w-11 h-6 bg-neutral-300 dark:bg-neutral-700 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white dark:after:bg-neutral-900 after:border-neutral-300 dark:after:border-neutral-700 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-500"></div>
                  </label>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1 dark:text-neutral-100">Scan Frequency</label>
                  <select className="w-full text-sm border border-neutral-300 dark:border-neutral-700 rounded px-3 py-1.5 bg-white dark:bg-neutral-900 text-neutral-700 dark:text-neutral-100">
                    <option>Daily</option>
                    <option>Weekly</option>
                    <option>Monthly</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1 dark:text-neutral-100">Scan Time</label>
                  <input 
                    type="time" 
                    className="w-full text-sm border border-neutral-300 dark:border-neutral-700 rounded px-3 py-1.5 bg-white dark:bg-neutral-900 text-neutral-700 dark:text-neutral-100"
                    defaultValue="01:00"
                  />
                  <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">UTC time zone</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;