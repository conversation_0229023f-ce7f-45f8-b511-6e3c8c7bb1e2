import React, { useState } from 'react';
import { FileText, Download, Calendar, AlertTriangle } from 'lucide-react';
import { useDashboard } from '../../context/DashboardContext';
import { motion } from 'framer-motion';

export type ApiScanData = {
  id: string;
  name: string;
  type: string;
  date: string;
  vulnerabilities?: any[];
  findings?: any[];
  vulns?: any[];
  summary?: {
    vulnerabilities?: number;
    highSeverity?: number;
    openPorts?: number;
    date?: string;
    // Add other summary fields as needed
  };
  // ...other properties
};

const RecentReports: React.FC = () => {
  const { scanFiles, rawApiData, stats } = useDashboard(); // <-- get rawApiData and stats here
  const [selectedFilter] = useState<'all' | 'Defendly ' | 'Advanced'>('all');

  const filteredFiles = selectedFilter === 'all'
    ? scanFiles
    : scanFiles.filter(file => file.type === selectedFilter);

  // Download the entire backend JSON
  const handleDownloadJSON = () => {
    if (!rawApiData) return;
    const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(rawApiData, null, 2));
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute("href", dataStr);
    downloadAnchorNode.setAttribute("download", "full-scan-report.json");
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  };




  // // Download the HTML report
const handleDownloadHTML = () => {
  if (!rawApiData) return;

  // Helper: escape HTML entities
  const escapeHTML = (str = "") =>
    String(str)
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;");

  // Helper: render alerts
  type Alert = {
    name?: string;
    alert?: string;
    riskdesc?: string;
    riskcode?: string;
    desc?: string;
    confidence?: string;
    cweid?: string;
    solution?: string;
    instances?: Array<{ uri?: string; param?: string } & Record<string, any>>;
    [key: string]: any;
  };
  const renderAlerts = (alerts: Alert[] = []) => {
    if (!alerts.length) return `<div>No security alerts found.</div>`;
    return alerts
      .map(
        (a, i) => {
          const instances = Array.isArray(a.instances) ? a.instances : [];
          const alertId = `alert-instances-${i}`;
          return `
        <div class="accordion-item">
          <div class="accordion-header">${escapeHTML(a.name || a.alert || `Alert #${i + 1}`)}
            <span class="tag ${
              /high/i.test(a.riskdesc || a.riskcode || "") ? "high" : /med/i.test(a.riskdesc || a.riskcode || "") ? "med" : /low/i.test(a.riskdesc || a.riskcode || "") ? "low" : "info"
            }">${escapeHTML(a.riskdesc || a.riskcode || "")}</span>
          </div>
          <div class="accordion-content">
            <div><b>Description:</b> ${a.desc ? a.desc.replace(/<[^>]+>/g, "") : "-"}</div>
            <div><b>Confidence:</b> ${escapeHTML(a.confidence || "-")}</div>
            <div><b>CWE:</b> ${escapeHTML(a.cweid || "-")}</div>
            <div><b>Solution:</b> ${a.solution ? a.solution.replace(/<[^>]+>/g, "") : "-"}</div>
            <div><b>Instances:</b> 
              <ul id="${alertId}" class="instance-list">
                ${
                  instances
                    .map(
                      (inst) =>
                        `<li>${escapeHTML(inst.uri || inst.param || JSON.stringify(inst) || "-")}</li>`
                    )
                    .join("")
                }
              </ul>
            </div>
          </div>
        </div>
      `;
        }
      )
      .join("");
  };

  // Helper: render attack surface
  const as = rawApiData.attack_surface_index?.metrics || {};
  const renderAttackSurface = () => `
    <table>
      <tr>
        <th>Subdomains</th>
        <th>Public IPs</th>
        <th>Open Ports</th>
        <th>Exposed Services</th>
      </tr>
      <tr>
        <td>${(as.detected_subdomains || []).map((s) => `<li>${escapeHTML(s)}</li>`).join("")}</td>
        <td>${(as.public_ips || []).map((ip) => `<li>${escapeHTML(ip)}</li>`).join("")}</td>
        <td>${(as.open_ports || []).map((p) => `<li>${escapeHTML(String(p))}</li>`).join("")}</td>
        <td>${(as.exposed_services || []).map((svc) => `<li>${escapeHTML(svc)}</li>`).join("")}</td>
      </tr>
    </table>
  `;

  // Helper: render compliance
  const comp = rawApiData.compliance_readiness || {};
  const renderCompliance = () => `
    <table>
      <tr>
        <th>CIS Compliance</th>
        <th>OWASP Compliance</th>
        <th>Total CIS Issues</th>
        <th>Total OWASP Issues</th>
        <th>Risk Level</th>
      </tr>
      <tr>
        <td>${comp.cis_compliance ?? "-"}</td>
        <td>${comp.owasp_compliance ?? "-"}</td>
        <td>${comp.total_cis_issues ?? "-"}</td>
        <td>${comp.total_owasp_issues ?? "-"}</td>
        <td>${escapeHTML(rawApiData.vendor_risk_rating?.risk_level || "-")}</td>
      </tr>
    </table>
    <div class="compliance-issues">
      <b>CIS Findings:</b> ${Object.entries(comp.cis_findings || {}).map(([k, v]) => `${escapeHTML(k)} (${v})`).join(", ")}<br>
      <b>OWASP Findings:</b> ${Object.entries(comp.owasp_findings || {}).map(([k, v]) => `${escapeHTML(k)} (${v})`).join(", ")}
    </div>
  `;

  // Helper: render threat intelligence
  const ti = rawApiData.threat_intelligence?.reputation_data || {};
  const tis = rawApiData.threat_intelligence?.threat_intelligence_score || {};
  const renderThreatIntel = () => {
    const analysisStats = ti.analysis_stats || {};
    return `
    <table>
      <tr>
        <th>Reputation Score</th>
        <th>Assessment</th>
        <th>Engines (Malicious/Suspicious/Total)</th>
        <th>Threat Score</th>
        <th>Risk Assessment</th>
      </tr>
      <tr>
        <td>${tis.score ?? "-"}</td>
        <td>${escapeHTML(tis.assessment || "-")}</td>
        <td>${analysisStats.malicious ?? "-"} / ${analysisStats.suspicious ?? "-"} / ${analysisStats.total_engines ?? "-"}</td>
        <td>${ti.threat_score ?? "-"}</td>
        <td>${escapeHTML(ti.risk_assessment || "-")}</td>
      </tr>
    </table>
    <div style="margin-top:8px;">
      <b>Analysis date:</b> ${escapeHTML(ti.last_analysis || "-")}
    </div>
  `;
  };

  // Helper: render endpoints
  const endpoints = (rawApiData.endpoints?.endpoints || []).slice(0, 20);
  const renderEndpoints = () => `
    <details>
      <summary>Show endpoints list</summary>
      <ul class="endpoint-list">
        ${endpoints.map((e) => `<li>${escapeHTML(e)}</li>`).join("")}
        ${(rawApiData.endpoints?.total_count || 0) > 20 ? "<li>...and more</li>" : ""}
      </ul>
    </details>
  `;

  // Scores and metrics
  const hygiene = rawApiData.cyber_hygiene_score || {};
  const vendor = rawApiData.vendor_risk_rating || {};
  const compScore = rawApiData.compliance_readiness?.overall_compliance_score ?? "-";
  const combined = rawApiData.combined_security_score ?? "-";
  const threatScore = tis.score ?? "-";

  // Main HTML content
  // Embed Defendly logo SVG as data URI (actual logo)
  const defendlyLogoSVG = `<?xml version="1.0" encoding="UTF-8"?>\n<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">\n<!-- Creator: CorelDRAW X7 -->\n<svg xmlns="http://www.w3.org/2000/svg" xml:space="preserve" width="1043mm" height="199.906mm" version="1.1" style="shape-rendering:geometricPrecision; text-rendering:geometricPrecision; image-rendering:optimizeQuality; fill-rule:evenodd; clip-rule:evenodd"\nviewBox="0 0 69850 13388"\n xmlns:xlink="http://www.w3.org/1999/xlink">\n <defs>\n  <style type="text/css">\n   <![CDATA[\n    .fil3 {fill:#5AC15B}\n    .fil2 {fill:#DC332A}\n    .fil4 {fill:#FED806}\n    .fil0 {fill:url(#id0)}\n    .fil1 {fill:url(#id1);fill-rule:nonzero}\n   ]]>\n  </style>\n  <linearGradient id="id0" gradientUnits="userSpaceOnUse" x1="11741" y1="5595.76" x2="-49.3706" y2="7792.01">\n   <stop offset="0" style="stop-opacity:1; stop-color:#00457F"/>\n   <stop offset="1" style="stop-opacity:1; stop-color:#1F1B20"/>\n  </linearGradient>\n  <linearGradient id="id1" gradientUnits="userSpaceOnUse" x1="68267.7" y1="4829.76" x2="15786.4" y2="8557.94">\n   <stop offset="0" style="stop-opacity:1; stop-color:#204691"/>\n   <stop offset="1" style="stop-opacity:1; stop-color:black"/>\n  </linearGradient>\n </defs>\n <g id="Layer_x0020_1">\n  <metadata id="CorelCorpID_0Corel-Layer"/>\n  <path class="fil0" d="M1064 2784c784,-206 1632,-569 2410,-821 390,-127 2153,-818 2384,-825 205,-6 4206,1455 4774,1645 -52,1559 -50,3505 -552,5006 -101,300 -440,1082 -597,1199l20 -3359 -1210 2 0 4758c-51,133 -669,687 -855,787l-4 -2892 -1116 -4 -56 3691c-403,303 -466,262 -889,-2l-22 -4290 -1121 -1 -31 3497 -1476 -1459c-404,-503 -808,-1085 -1064,-1818 -522,-1494 -613,-3473 -595,-5114zm4724 -2784l-4624 1653c-1342,465 -1167,182 -1144,2000 21,1742 214,3747 967,5301 231,477 736,1269 1092,1654 523,565 1212,1212 1863,1659 268,185 1660,1184 1991,1118 260,-52 1713,-1042 1927,-1213l855 -710c321,-278 522,-441 795,-776 584,-714 788,-908 1265,-1871 720,-1452 902,-3719 906,-5358 5,-1737 195,-1330 -1281,-1860 -454,-163 -4561,-1610 -4612,-1597z\"/>\n  <path class="fil1" d="M14204 3427l0 6537 3269 0c902,0 1718,-365 2308,-960 65,-67 128,-135 187,-205 484,-567 772,-1305 772,-2105 0,-1806 -1464,-3267 -3267,-3267l-3269 0zm1164 1165l2105 0c1157,0 2102,945 2102,2102 0,1161 -945,2105 -2102,2105l-2105 0 0 -4207zm8072 -3l4773 0 0 -1165 -4773 0c-975,0 -1763,793 -1763,1767l0 3005c0,975 788,1768 1763,1768l4773 0 0 -1165 -4773 0c-330,0 -598,-273 -598,-603l0 -1234 4207 0 0 -1165 -4207 0 0 -606c0,-330 268,-602 598,-602l0 0zm7473 -1165c-974,0 -1762,793 -1762,1767l0 4769 1161 0 0 -2998 4210 0 0 -1165 -4207 0 0 -606c0,-330 268,-602 598,-602l4774 0 0 -1165 -4774 0zm7474 1165l4774 0 0 -1165 -4774 0c-975,0 -1763,793 -1763,1767l0 3005c0,975 788,1768 1763,1768l4774 0 0 -1165 -4774 0c-330,0 -598,-273 -598,-603l0 -1234 4207 0 0 -1165 -4207 0 0 -606c0,-330 268,-602 598,-602l0 0zm11082 -1165l0 4164 -4202 -3239 -1169 -901 0 6516 1169 0 0 -4140 4202 3240 1168 900 0 -6540 -1168 0zm5372 2l-3270 0 0 6536 3270 0c901,0 1717,-364 2307,-959 66,-66 128,-136 187,-206 485,-566 773,-1304 773,-2105 0,-1806 -1464,-3266 -3267,-3266l0 0zm0 5371l-2105 0 0 -4205 2105 0c1157,0 2102,943 2102,2100 0,1162 -945,2105 -2102,2105l0 0zm6169 -1c-416,-55 -746,-385 -801,-801l0 -4571 -1164 0 0 4455c0,330 78,640 218,917 198,414 531,749 946,951 276,136 587,213 918,213l3313 0 0 -1164 -3430 0zm7372 -5384l-1798 2331 -1799 -2331 -1471 0 2532 3282 4 5 156 197 0 3064 1168 0 0 -3084 140 -177 5 -5 2531 -3278 0 -4 -1468 0zm-5068 6564l16 -20 -16 0 0 20zm6536 -5l0 -15 -12 0 12 15z"/>\n  <g id="_2144397220608">\n   <path class="fil2" d="M6446 6332c12,-24 28,-47 49,-68l1887 -1979c-49,-92 -76,-197 -76,-308 0,-372 309,-673 690,-673 381,0 690,301 690,673 0,372 -309,674 -690,674 -66,0 -129,-9 -190,-26l-1910 2003c-3,4 -7,7 -10,11l-440 -307z"/>\n   <path class="fil3" d="M5479 5917l527 415c113,-77 250,-123 398,-123 360,0 690,310 690,674 0,372 -309,674 -690,674 -381,0 -690,-302 -690,-674 0,-29 2,-58 5,-87l-531 -416 291 -463z"/>\n   <path class="fil4" d="M4790 5211c381,0 690,302 690,674 0,5 0,11 0,16 0,6 -1,11 -1,16 -66,356 -309,641 -689,641 -81,0 -159,-13 -231,-38l-1657 1515c-301,275 -628,-161 -376,-392l1631 -1491c-37,-82 -57,-172 -57,-267 0,-372 309,-674 690,-674z"/>\n  </g>\n </g>\n</svg>\n`;
  // TODO: Replace above with actual SVG content as data URI
  const htmlContent = `
  <!DOCTYPE html>
  <html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>Security Assessment Report</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
      body { font-family: 'Segoe UI', Arial, sans-serif; background: #f7f9fb; color: #222; margin: 0; }
      .container { max-width: 90%; margin: 36px auto; background: #fff; border-radius: 16px; box-shadow: 0 4px 28px rgba(0,30,60,.06); padding: 36px 30px; }
      .report-header { margin-bottom: 0; }
      .report-logo { height: 48px; width: auto; margin-right: 18px; }
      h1 { font-size: 2.4rem; margin-bottom: 0.5em; color: #00457F;}
      h2 { color: #17496f; margin-top: 2em; font-size: 1.25rem;}
      .summary-cards { display: flex; gap: 26px; flex-wrap: wrap; margin-bottom: 34px; }
      .card { flex: 1 1 210px; padding: 24px; background: #f5f8fb; border-radius: 12px; box-shadow: 0 2px 12px rgba(0,30,60,0.07); min-width: 210px; text-align: center; border: 2px solid #f1f3f6;}
      .card h3 { margin: 0 0 8px 0; font-size: 1.07em; color: #244b6b;}
      .score { font-size: 2.6em; font-weight: 700; color: #00457F;}
      .grade { font-size: 1.35em; font-weight: bold; margin-top: 7px; }
      .grade.D { color: #db304a;}
      .grade.C { color: #ff9900;}
      .risk-badge { display: inline-block; background: #ffe0e0; color: #b3261e; border-radius: 6px; padding: 2px 12px; font-size: 0.97em; margin-top: 7px;}
      .section { margin-bottom: 48px; }
      .accordion { border-radius: 10px; overflow: hidden; }
      .accordion-item { background: #f7fafd; border-bottom: 1px solid #e3e9f2; }
      .accordion-header { padding: 18px 24px; cursor: pointer; font-weight: 600; font-size: 1.05em; color: #19456b; transition: background 0.12s; position: relative;}
      .accordion-header .tag { float: right; }
      .accordion-header:hover { background: #e4ecf9;}
      .accordion-content { display: none; padding: 17px 32px; background: #fff;}
      .accordion-item.active .accordion-content { display: block;}
      .tag { background: #e4ecf9; color: #18436b; padding: 3px 12px; border-radius: 5px; font-size: 0.95em; margin-left: 5px;}
      .low { background: #e8f9e6; color: #208442;}
      .med { background: #fff6d6; color: #db9200;}
      .high { background: #ffe3e3; color: #d2344a;}
      .info { background: #f3f5f7; color: #7388a4;}
      .table-responsive { overflow-x: auto; }
      table { border-collapse: collapse; width: 100%; margin: 8px 0; background: #fff; }
      th, td { border: 1px solid #e2e7ed; padding: 9px 14px; text-align: left; }
      th { background: #f5f7fa; color: #18436b;}
      tr:nth-child(even) td { background: #f8fafd;}
      .endpoint-list { list-style: none; padding: 0; margin: 0; font-size: 0.99em;}
      .endpoint-list li { margin-bottom: 3px; color: #496186;}
      details summary { cursor: pointer; font-weight: 500; color: #00457F; }
      details[open] summary:after { content: "▲"; margin-left: 6px; font-size: 0.92em; }
      details summary:after { content: "▼"; margin-left: 6px; font-size: 0.92em;}
      .footer { text-align: center; font-size: 0.96em; color: #aaa; margin-top: 40px;}
      .compliance-issues { background: #fcf8e3; padding: 8px 14px; border-radius: 6px; margin: 7px 0; display: inline-block; font-size: 1em;}
      @media (max-width: 950px) { .summary-cards { flex-direction: column; gap: 13px;}}
      @media (max-width: 640px) { .container { padding: 14px 3vw;}}
    </style>
    <script>
      window.onload = function() {
        document.querySelectorAll('.accordion-header').forEach(header => {
          header.onclick = function() {
            this.parentElement.classList.toggle('active');
          };
        });
      };
      function showAllInstances(alertId, hiddenInstancesStr) {
        var ul = document.getElementById(alertId);
        if (!ul) return;
        var hiddenInstances = JSON.parse(decodeURIComponent(hiddenInstancesStr));
        hiddenInstances.forEach(function(inst) {
          var li = document.createElement('li');
          li.textContent = inst.uri || inst.param || JSON.stringify(inst) || '-';
          ul.appendChild(li);
        });
        // Remove the '...and more' link after expanding
        var moreLink = ul.querySelector('.show-more-instances');
        if (moreLink) moreLink.parentNode.remove();
      }
    </script>
  </head>
  <body>
    <div class="container">
      <div class="report-header">
        <img src="data:image/svg+xml;utf8,${encodeURIComponent(defendlyLogoSVG)}" alt="Defendly Logo" class="report-logo" />
      </div>
      <h1 style="margin-top: 0; margin-bottom: 0.5em; color: #00457F;">Security Assessment Report</h1>
      <p><strong>URL:</strong> ${escapeHTML(rawApiData.url || "-")}<br>
        <strong>Scan Date:</strong> ${escapeHTML(rawApiData.scan_date || rawApiData.scanDate || "-")}<br>
        <strong>Status:</strong> ${escapeHTML(rawApiData.status || "-")}<br>
        <strong>Total Endpoints:</strong> ${rawApiData.endpoints?.total_count || "-"}
      </p>
      <div class="summary-cards">
        <div class="card">
          <h3>Cyber Hygiene Score</h3>
          <div class="score">${hygiene.score ?? "-"}</div>
          <div class="grade ${hygiene.grade || ""}">Grade ${hygiene.grade || "-"}</div>
        </div>
        <div class="card">
          <h3>Vendor Risk Rating</h3>
          <div class="score">${vendor.letter_grade || "-"}</div>
          <div class="risk-badge">${vendor.risk_level || "-"}</div>
        </div>
        <div class="card">
          <h3>Total Vulnerabilities</h3>
          <div class="score">${Array.isArray(rawApiData.alerts) ? rawApiData.alerts.length : 0}</div>
        </div>
        <div class="card">
          <h3>Combined Security Score</h3>
          <div class="score">${combined}</div>
        </div>
        <div class="card">
          <h3>Threat Intelligence</h3>
          <div class="score">${threatScore}</div>
          <span class="tag med">${tis.assessment || "-"}</span>
        </div>
      </div>
      <div class="section">
        <h2>Security Findings & Alerts</h2>
        <div class="accordion">
          ${renderAlerts(rawApiData.alerts || [])}
        </div>
      </div>
      <div class="section">
        <h2>Attack Surface Overview</h2>
        <div class="table-responsive">
          ${renderAttackSurface()}
        </div>
        <div style="margin-top:17px;">
          <strong>Endpoints Scanned (${rawApiData.endpoints?.total_count || "0"}):</strong>
          ${renderEndpoints()}
        </div>
      </div>
      <div class="section">
        <h2>Compliance & Risk Ratings</h2>
        <div class="table-responsive">
          ${renderCompliance()}
        </div>
      </div>
      <div class="section">
        <h2>Threat Intelligence</h2>
        <div class="table-responsive">
          ${renderThreatIntel()}
        </div>
      </div>
      <div class="footer">
        <hr style="margin: 28px auto;">
        <div>
          <strong>Security Report generated by Defendly | Bug Hunters | 2025</strong>
        </div>
      </div>
    </div>
  </body>
  </html>
  `;

  const blob = new Blob([htmlContent], { type: "text/html" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "defendly-scan-report.html";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};
 // // Download the HTML report END





  return (
    <div className="container mx-auto px-4 py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-neutral-800 dark:text-neutral-100">Recent Reports</h1>
        <p className="text-neutral-600 dark:text-neutral-300">View and download your scan reports</p>
      </div>

      {/* <div className="mb-6 flex space-x-2">
        <button
          className={`px-4 py-2 rounded-md transition-colors ${
            selectedFilter === 'all'
              ? 'bg-primary-500 text-white'
              : 'bg-white dark:bg-neutral-900 text-neutral-600 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800'
          }`}
          onClick={() => setSelectedFilter('all')}
        >
          All Reports
        </button>
        <button
          className={`px-4 py-2 rounded-md transition-colors ${
            selectedFilter === 'Defendly '
              ? 'bg-primary-500 text-white'
              : 'bg-white dark:bg-neutral-900 text-neutral-600 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800'
          }`}
          onClick={() => setSelectedFilter('Defendly ')}
        >
          Defendly Scans
        </button>
        <button
          className={`px-4 py-2 rounded-md transition-colors ${
            selectedFilter === 'Advanced'
              ? 'bg-primary-500 text-white'
              : 'bg-white dark:bg-neutral-900 text-neutral-600 dark:text-neutral-200 hover:bg-neutral-100 dark:hover:bg-neutral-800'
          }`}
          onClick={() => setSelectedFilter('Advanced')}
        >
          Advanced Scans
        </button>
      </div> */}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredFiles.length > 0 ? (
          filteredFiles.map((file) => (
            <motion.div
              key={file.id}
              className="bg-white dark:bg-[#202020] rounded-lg shadow-card hover:shadow-card-hover transition-shadow"
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <div className="p-5">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-start">
                    <div
                      className={`w-10 h-10 rounded-lg mr-3 flex items-center justify-center ${
                        file.type === 'Defendly '
                          ? 'bg-primary-100 dark:bg-primary-900 text-primary-600 dark:text-primary-300'
                          : 'bg-secondary-100 dark:bg-secondary-900 text-secondary-600 dark:text-secondary-300'
                      }`}
                    >
                      <FileText className="w-5 h-5" />
                    </div>
                    <div>
                      <h3 className="font-medium text-neutral-800 dark:text-neutral-100">{file.name}</h3>
                      <div className="text-xs text-neutral-500 dark:text-neutral-400 flex items-center mt-1">
                        <Calendar className="w-3 h-3 mr-1" />
                        {file.date}
                      </div>
                    </div>
                  </div>
                  <div
                    className={`text-xs font-medium px-2 py-1 rounded-full ${
                      file.type === 'Defendly '
                        ? 'bg-primary-100 dark:bg-primary-900 text-primary-800 dark:text-primary-200'
                        : 'bg-secondary-100 dark:bg-secondary-900 text-secondary-800 dark:text-secondary-200'
                    }`}
                  >
                    {file.type.toUpperCase()}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="bg-neutral-50 dark:bg-[#171717] p-3 rounded">
                    <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
                      {file.summary.vulnerabilities}
                    </div>
                    <div className="text-xs text-neutral-600 dark:text-neutral-300">Vulnerabilities</div>
                  </div>

                  <div className="bg-neutral-50 dark:bg-[#171717] p-3 rounded">
                    <div className="text-2xl font-bold text-danger-600 dark:text-danger-400">
                      {file.summary.openPorts ?? stats.openPorts ?? 0}
                    </div>
                    <div className="text-xs text-neutral-600 dark:text-neutral-300">Open Ports</div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <button
                    className="flex-1 bg-neutral-100 dark:bg-[#171717] hover:bg-neutral-200 dark:hover:bg-neutral-700 text-neutral-700 dark:text-neutral-100 py-2 rounded flex items-center justify-center transition-colors"
                    onClick={handleDownloadJSON}
                    disabled={!rawApiData}
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download JSON
                  </button>
                  <button
                    className="flex-1 bg-neutral-100 dark:bg-[#171717] hover:bg-neutral-200 dark:hover:bg-neutral-700 text-neutral-700 dark:text-neutral-100 py-2 rounded flex items-center justify-center transition-colors"
                    onClick={handleDownloadHTML}
                    disabled={!rawApiData}
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Download HTML
                  </button>
                </div>
                
              </div>
            </motion.div>
          ))
        ) : (
          <div className="col-span-full flex flex-col items-center justify-center py-12 text-neutral-500 dark:text-neutral-400">
            <AlertTriangle className="w-12 h-12 mb-3" />
            <h3 className="text-lg font-medium mb-1 dark:text-neutral-100">No Reports Found</h3>
            <p>Upload scan data to generate reports</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentReports;