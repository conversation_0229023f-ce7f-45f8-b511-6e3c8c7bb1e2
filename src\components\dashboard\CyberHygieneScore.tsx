// import React from "react";
// import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts";
// import { useDashboard } from "../../context/DashboardContext";
// import { Info } from "lucide-react";

// const CyberHygieneScore = () => {
//   const { stats, isLoading } = useDashboard();

//   const score = stats?.cyberHygieneScore ?? 0;

//   const createGradientSegments = (score: number | null) => {
//     const segments = [];
//     const totalSegments = 100;

//     for (let i = 0; i < totalSegments; i++) {
//       const percentage = i / totalSegments;
//       let color;

//       if (score !== null && i < score) {
//         if (percentage < 0.3) color = "#10B981";
//         else if (percentage < 0.6) color = "#F59E0B";
//         else color = "#EF4444";
//       } else {
//         color = "#374151";
//       }

//       segments.push({ name: `segment-${i}`, value: 1, color });
//     }

//     return segments;
//   };

//   const getRiskLevel = (score: number | null) => {
//     if (score === null) return "";
//     if (score >= 80) return "Low Risk";
//     if (score >= 60) return "Moderate Risk";
//     if (score >= 40) return "Moderate Risk - Action Needed";
//     return "High Risk - Immediate Action Required";
//   };

//   const segments = createGradientSegments(score);

//   return (
//     <div className="bg-gray-900 dark:bg-gray-900 text-white dark:text-white rounded-2xl p-8 min-h-[400px] relative overflow-visible">
//       {/* Decorations */}
//       <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-red-500/20 to-transparent rounded-full blur-xl pointer-events-none"></div>
//       <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-green-500/20 to-transparent rounded-full blur-xl pointer-events-none"></div>

//       {/* Header with Info Icon */}
//       <div className="flex justify-between items-start mb-6">
//         <div className="flex items-center gap-2 relative group cursor-help">
//           <h2 className="text-2xl font-bold select-none">
//             Cyber Hygiene Score
//           </h2>
//           <Info size={18} className="text-gray-400" />
//           {/* Tooltip */}
//           <div className="absolute top-full left-1/2 -translate-x-1/2 mt-1 w-64 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm text-gray-900 dark:text-gray-100 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-20">
//             Overall risk posture based on vulnerabilities, misconfigurations,
//             and exposed assets.
//           </div>
//         </div>
//         <button className="text-gray-400 hover:text-white text-sm border border-gray-600 px-4 py-1 rounded">
//           View Details
//         </button>
//       </div>

//       {/* Subtext */}
//       <p className="text-gray-300 text-sm mb-8 leading-relaxed">
//         Let Fillow manage your project automatically with our best AI systems
//       </p>

//       {/* Score and Chart */}
//       <div className="flex items-center justify-between">
//         {/* CTA Button */}
//         <div className="flex-1 flex mt-6">
//           <button className="bg-white text-gray-900 dark:bg-white dark:text-gray-900 px-6 py-3 rounded-full font-medium hover:bg-gray-100 transition-colors">
//             Try Free Now
//           </button>
//         </div>

//         {/* Pie Chart */}
//         <div className="relative w-64 h-64">
//           <ResponsiveContainer width="100%" height="100%">
//             <PieChart>
//               <Pie
//                 data={segments}
//                 cx="50%"
//                 cy="50%"
//                 startAngle={90}
//                 endAngle={-270}
//                 innerRadius="75%"
//                 outerRadius="95%"
//                 paddingAngle={0}
//                 dataKey="value"
//                 stroke="none"
//               >
//                 {segments.map((entry, index) => (
//                   <Cell key={`cell-${index}`} fill={entry.color} />
//                 ))}
//               </Pie>
//             </PieChart>
//           </ResponsiveContainer>

//           {/* Center Score */}
//           <div className="absolute inset-0 flex flex-col items-center justify-center pointer-events-none">
//             {isLoading ? (
//               <span className="text-lg animate-pulse text-gray-400">
//                 Loading...
//               </span>
//             ) : (
//               <>
//                 <span className="text-6xl font-bold text-white mb-2 select-none">
//                   {score}
//                 </span>
//                 <span className="text-gray-300 text-sm select-none">
//                   {getRiskLevel(score)}
//                 </span>
//               </>
//             )}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default CyberHygieneScore;

import { PieChart, Pie, Cell, ResponsiveContainer } from "recharts";
import { Info } from "lucide-react";
import { useDashboard } from "../../context/DashboardContext";

const CyberHygieneScore = () => {
  const { stats, loadingData } = useDashboard();
  const isLoading = loadingData;

  const score = stats?.cyberHygieneScore ?? 0;

  // Create gradient segments for smooth color transition
  const createGradientSegments = (score: number) => {
    const segments = [];
    const filledSegments = Math.floor(score);
    
    // Create filled segments with gradient colors
    for (let i = 0; i < filledSegments; i++) {
      let color;
      const percentage = i / 100;
      
      if (percentage < 0.4) {
        color = "#FF0004"; // Red
      } else if (percentage < 0.6) {
        // Transition from red to yellow
        const t = (percentage - 0.4) / 0.2;
        color = `rgb(${255}, ${Math.floor(117 + 138 * t)}, 4)`;
      } else if (percentage < 0.8) {
        // Yellow to green transition
        const t = (percentage - 0.6) / 0.2;
        color = `rgb(${Math.floor(255 - 131 * t)}, ${Math.floor(255 - 57 * t)}, ${Math.floor(4 + 55 * t)})`;
      } else {
        color = "#7CF63B"; // Green
      }
      
      segments.push({
        name: `filled-${i}`,
        value: 1,
        color: color,
      });
    }
    
    // Add empty segments
    for (let i = filledSegments; i < 100; i++) {
      segments.push({
        name: `empty-${i}`,
        value: 1,
        color: "#404040",
      });
    }
    
    return segments;
  };

  const getRiskLevel = (score: number | null) => {
    if (score === null) return "";
    if (score >= 80) return "Low Risk - Well Protected";
    if (score >= 60) return "Moderate Risk - Action Needed";
    if (score >= 40) return "High Risk - Immediate Attention";
    return "Critical Risk - Urgent Action Required";
  };

  const segments = createGradientSegments(score);

  return (
    <div className="w-full">
      <div className="relative text-neutral-900 dark:text-white shadow-sm border overflow-hidden bg-white dark:bg-[#202020] border-neutral-200 dark:border-[#232323] rounded-[10px]">
        {/* Container with proper aspect ratio and responsive layout */}
        <div className="w-full min-h-[250px] sm:min-h-[280px] lg:h-[320px] p-4 sm:p-6 lg:p-8 flex flex-col lg:flex-row items-start justify-between gap-4 lg:gap-6">

          {/* Top-right View Details button */}
          {/* <button className="absolute top-4 right-4 bg-transparent border border-neutral-400 dark:border-neutral-500 rounded-md px-2 py-1 text-xs text-neutral-700 dark:text-neutral-200 hover:border-neutral-500 dark:hover:border-neutral-400 transition-colors z-10">
            View Details
          </button> */}

          {/* Left Content */}
          <div className="flex-1 w-full lg:w-auto flex flex-col justify-between text-center lg:text-left order-2 lg:order-1">
            {/* Top Text Content */}
            <div>
              {/* Heading */}
              <div className="flex items-center justify-center lg:justify-start gap-1 mb-3">
                <h2 className="text-lg sm:text-xl lg:text-2xl font-bold select-none text-neutral-900 dark:text-white">Cyber Hygiene Score</h2>
                <div className="tooltip-container relative group">
                  <Info size={16} className="text-neutral-500 dark:text-neutral-400 sm:w-[18px] sm:h-[18px] cursor-help" />
                  <div className="tooltip absolute left-0 top-6 bg-white dark:bg-neutral-900 p-3 rounded-lg shadow-lg text-sm z-10 hidden group-hover:block w-64 text-neutral-900 dark:text-white">
                    Overall security posture based on vulnerabilities, misconfigurations, and exposed assets. Higher scores indicate better security hygiene.
                  </div>
                </div>
              </div>
              
              {/* Subtext */}
              <p className="text-neutral-600 dark:text-neutral-400 text-sm sm:text-base leading-relaxed max-w-[300px] sm:max-w-[350px] mx-auto lg:mx-0">
                Reflects your organization’s overall security posture. Aim for a higher score.
              </p>
            </div>
            
            {/* CTA Button - Bottom */}
            <div className="flex justify-center lg:justify-start mt-6 lg:mt-0 lg:mb-2 lg:absolute lg:bottom-16 lg:left-8 w-full">
              <button className="bg-white dark:bg-white text-neutral-900 dark:text-neutral-900 px-6 sm:px-8 py-2.5 sm:py-3 rounded-full font-medium shadow-sm hover:bg-neutral-50 dark:hover:bg-neutral-100 transition-colors text-sm sm:text-base border border-neutral-200 dark:border-neutral-300 min-w-[140px] sm:min-w-[160px] h-[44px] sm:h-[48px]">
                Improve Score
              </button>
            </div>
          </div>

          {/* Pie Chart Container */}
          <div className="flex-shrink-0 order-1 lg:order-2 w-full max-w-[200px] sm:max-w-[220px] lg:max-w-[240px] flex flex-col items-center self-center lg:self-start">
            {/* Pie Chart */}
            <div className="relative w-full aspect-square flex items-center justify-center mb-3">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={segments}
                    cx="50%"
                    cy="50%"
                    startAngle={90}
                    endAngle={-270}
                    innerRadius="70%"
                    outerRadius="90%"
                    paddingAngle={0}
                    dataKey="value"
                    stroke="none"
                  >
                    {segments.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>

              {/* Center Score Only */}
              <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                {isLoading ? (
                  <span className="text-base sm:text-lg animate-pulse text-neutral-600 dark:text-neutral-400">
                    Loading...
                  </span>
                ) : (
                  <span className="text-3xl sm:text-4xl lg:text-6xl font-bold text-neutral-900 dark:text-white select-none">
                    {score}
                  </span>
                )}
              </div>
            </div>

            {/* Risk Level Text Below Chart */}
            <div className="text-center w-full">
              {!isLoading && score !== null && (
                <span className="text-neutral-600 dark:text-neutral-400 text-xs sm:text-sm select-none">
                  {getRiskLevel(score)}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CyberHygieneScore;