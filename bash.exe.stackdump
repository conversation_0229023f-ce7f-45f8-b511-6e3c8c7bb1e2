Stack trace:
Frame         Function      Args
0007FFFF78F0  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF67F0) msys-2.0.dll+0x2118E
0007FFFF78F0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF7BC8) msys-2.0.dll+0x69BA
0007FFFF78F0  0002100469F2 (00021028DF99, 0007FFFF77A8, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF78F0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF78F0  00021006A545 (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF7BD0  00021006B9A5 (0007FFFF7900, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA923E0000 ntdll.dll
7FFA90BA0000 KERNEL32.DLL
7FFA8F530000 KERNELBASE.dll
7FFA90610000 USER32.dll
7FFA8F9C0000 win32u.dll
7FFA90F40000 GDI32.dll
7FFA8FED0000 gdi32full.dll
7FFA90010000 msvcp_win.dll
7FFA8F9F0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA90A80000 advapi32.dll
7FFA921E0000 msvcrt.dll
7FFA91BA0000 sechost.dll
7FFA90E20000 RPCRT4.dll
7FFA8EAB0000 CRYPTBASE.DLL
7FFA8F920000 bcryptPrimitives.dll
7FFA908D0000 IMM32.DLL
