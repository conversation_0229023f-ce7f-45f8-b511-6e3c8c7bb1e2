import React, { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { Vulnerability } from "../../types/types"; // Adjust path if necessary
interface Props {
  vulnerabilities: Vulnerability[];
}
const BASE_WIDTH = 870; // Your design's base width in px
// const BASE_HEIGHT = 319; // Your design's base height in px
const VulnerabilitySummaryCard: React.FC<Props> = ({ vulnerabilities }) => {
  const navigate = useNavigate();
  const containerRef = useRef<HTMLDivElement>(null);
  const [scale, setScale] = useState(1);
  useEffect(() => {
    const updateScale = () => {
      if (containerRef.current) {
        // Determine available width of the container
        const availableWidth = containerRef.current.offsetWidth;
        // Compute scale factor (never exceed 1)
        const newScale =
          availableWidth < BASE_WIDTH ? availableWidth / BASE_WIDTH : 1;
        setScale(newScale);
      }
    };
    updateScale();
    // Use ResizeObserver for modern browsers
    const resizeObserver = new ResizeObserver(() => updateScale());
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    // Fallback: update on window resize
    window.addEventListener("resize", updateScale);
    return () => {
      window.removeEventListener("resize", updateScale);
      if (containerRef.current) {
        resizeObserver.unobserve(containerRef.current);
      }
    };
  }, []);
  const severityCounts = vulnerabilities.reduce<Record<string, number>>(
    (acc, vuln) => {
      const sev = vuln.severity.toLowerCase();
      acc[sev] = (acc[sev] || 0) + 1;
      return acc;
    },
    {}
  );
  const total = vulnerabilities.length;
  const getRiskMessage = () => {
    const critical = severityCounts["critical"] || 0;
    const high = severityCounts["high"] || 0;
    if (critical > 0) return "High Risk - Immediate Attention Required";
    if (high > 0) return "Moderate Risk - Action Needed";
    return "Low Risk - Monitor";
  };
return (
  <div className="w-full">
    <div className="relative h-full bg-white dark:bg-[#202020] rounded-[10px] p-3 sm:p-4 md:p-6 text-neutral-900 dark:text-white border border-neutral-200 dark:border-neutral-800">
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 sm:gap-3 md:gap-4">
        {[0, 1, 2, 3, 4, 5].map((i) => {
          const { label, value, color, trend } = [
            { label: "Total Vulnerabilities", value: total, color: "bg-[#FF0000]/80 dark:bg-[#ff0000]/50 text-white", trend: null },
            { label: "Critical", value: severityCounts["critical"] || 0, color: "border-[#7f1d1d]/50", trend: "+0.5%" },
            { label: "High", value: severityCounts["high"] || 0, color: "border-[#dc2626]/50", trend: "-5.5%" },
            { label: "Medium", value: severityCounts["medium"] || 0, color: "border-[#fbbf24]/50", trend: "+0.5%" },
            { label: "Low", value: severityCounts["low"] || 0, color: "border-[#16a34a]/50", trend: "-5.5%" },
            { label: "Info", value: severityCounts["info"] || 0, color: "border-[#0ea5e9]/50", trend: "+0.5%" },
          ][i];
          return (
            <div
              key={i}
              className={`rounded-[10px] ${color.includes("bg-") ? color : `border-2 ${color}`} flex flex-col justify-between px-3 py-2 sm:px-4 sm:py-2 w-full`}
            >
              {/* Top Label */}
              <div className="text-[15px] sm:text-[16px] font-medium">{label}</div>
              {/* Bottom Row: Trend & Value */}
              <div className="flex items-end justify-between mt-auto">
                {trend ? (
                  <div
                    className={`text-xs sm:text-sm font-semibold ${
                      trend.startsWith("+") ? "text-green-400" : "text-red-400"
                    }`}
                  >
                    {trend.startsWith("+") ? "▲" : "▼"} {trend}
                  </div>
                ) : (
                  <div></div>
                )}
                <div className="text-2xl sm:text-[32px] font-bold">{value}</div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  </div>
);
};

export default VulnerabilitySummaryCard;